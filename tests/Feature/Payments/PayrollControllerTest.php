<?php

declare(strict_types=1);

namespace Tests\Feature\Payments;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

/**
 * Payroll Controller Test
 *
 * Feature tests for the PayrollController endpoints.
 * Tests the migrated getOwnerPayroll functionality.
 */
class PayrollControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Skip authentication for API tests
        $this->withoutMiddleware();
    }

    /**
     * Test getting owner payroll data with valid parameters
     */
    public function test_get_owner_payroll_with_valid_parameters(): void
    {
        $response = $this->getJson('/api/payroll/owner-payroll?year=2024&owner_id=1');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data',
                    'meta' => [
                        'year',
                        'owner_id',
                        'path',
                        'total_records',
                    ],
                ]);
    }

    /**
     * Test getting owner payroll data without required year parameter
     */
    public function test_get_owner_payroll_without_year(): void
    {
        $response = $this->getJson('/api/payroll/owner-payroll');

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['year']);
    }

    /**
     * Test getting owner payroll data with invalid year
     */
    public function test_get_owner_payroll_with_invalid_year(): void
    {
        $response = $this->getJson('/api/payroll/owner-payroll', [
            'year' => 1999, // Below minimum
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['year']);
    }

    /**
     * Test getting owner payroll data with filter parameters
     */
    public function test_get_owner_payroll_with_filters(): void
    {
        $response = $this->getJson('/api/payroll/owner-payroll', [
            'year' => 2024,
            'filter_params' => [
                'payroll_farming' => [1, 2, 3],
                'owner_names' => 'John Doe',
                'egn' => '1234567890',
            ],
        ]);

        $response->assertStatus(200);
    }

    /**
     * Test getting owner payroll data with path parameter
     */
    public function test_get_owner_payroll_with_path(): void
    {
        $response = $this->getJson('/api/payroll/owner-payroll', [
            'year' => 2024,
            'owner_id' => 1,
            'path' => '1.2.3',
        ]);

        $response->assertStatus(200);
    }

    /**
     * Test payroll index endpoint
     */
    public function test_payroll_index(): void
    {
        $response = $this->getJson('/api/payroll', [
            'year' => 2024,
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data',
                    'meta' => [
                        'total',
                        'year',
                        'owner_id',
                        'path',
                    ],
                ]);
    }

    /**
     * Test payroll summary endpoint
     */
    public function test_payroll_summary(): void
    {
        $response = $this->getJson('/api/payroll/summary', [
            'year' => 2024,
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'total_owners',
                        'total_area',
                        'total_rent',
                        'total_paid',
                        'total_unpaid',
                        'total_overpaid',
                        'payment_rate',
                        'total_area_formatted',
                        'total_rent_formatted',
                        'total_paid_formatted',
                        'total_unpaid_formatted',
                        'total_overpaid_formatted',
                        'payment_rate_formatted',
                    ],
                    'meta' => [
                        'year',
                        'total_records',
                    ],
                ]);
    }

    /**
     * Test payroll export endpoint
     */
    public function test_payroll_export(): void
    {
        $response = $this->getJson('/api/payroll/export', [
            'year' => 2024,
            'format' => 'json',
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data',
                    'meta' => [
                        'total_records',
                        'export_ready',
                        'format',
                    ],
                ]);
    }

    /**
     * Test payroll export with invalid format
     */
    public function test_payroll_export_with_invalid_format(): void
    {
        $response = $this->getJson('/api/payroll/export', [
            'year' => 2024,
            'format' => 'invalid',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['format']);
    }

    /**
     * Test pagination parameters
     */
    public function test_payroll_with_pagination(): void
    {
        $response = $this->getJson('/api/payroll', [
            'year' => 2024,
            'page' => 1,
            'per_page' => 10,
        ]);

        $response->assertStatus(200);
    }

    /**
     * Test sorting parameters
     */
    public function test_payroll_with_sorting(): void
    {
        $response = $this->getJson('/api/payroll', [
            'year' => 2024,
            'sort' => 'owner_names',
            'order' => 'desc',
        ]);

        $response->assertStatus(200);
    }

    /**
     * Test invalid sorting parameters
     */
    public function test_payroll_with_invalid_sorting(): void
    {
        $response = $this->getJson('/api/payroll', [
            'year' => 2024,
            'sort' => 'invalid_field',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['sort']);
    }

    /**
     * Test no contract payments filter
     */
    public function test_payroll_with_no_contract_payments(): void
    {
        $response = $this->getJson('/api/payroll/owner-payroll', [
            'year' => 2024,
            'filter_params' => [
                'no_contract_payments' => true,
            ],
        ]);

        $response->assertStatus(200);
    }

    /**
     * Test complex filter combination
     */
    public function test_payroll_with_complex_filters(): void
    {
        $response = $this->getJson('/api/payroll/owner-payroll', [
            'year' => 2024,
            'owner_id' => 1,
            'filter_params' => [
                'payroll_farming' => [1, 2],
                'owner_names' => 'Test Owner',
                'egn' => '1234567890',
                'contract_id' => 123,
                'ekatte' => '12345',
                'kad_ident' => 'KAD123',
            ],
        ]);

        $response->assertStatus(200);
    }

    /**
     * Test error handling for server errors
     */
    public function test_payroll_error_handling(): void
    {
        // This would require mocking the action to throw an exception
        // For now, we'll test with invalid data that might cause issues
        $response = $this->getJson('/api/payroll/owner-payroll', [
            'year' => 2024,
            'owner_id' => -1, // Invalid owner ID
        ]);

        // Should either return empty data or handle gracefully
        $response->assertStatus(200);
    }
}
