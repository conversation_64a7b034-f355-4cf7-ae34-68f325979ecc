<?php

declare(strict_types=1);

namespace Tests\Unit\Actions\Payments;

use App\Actions\Payments\GetOwnerPayrollAction;
use App\Actions\Payments\GetPaymentsPlotsAction;
use App\Actions\Payments\ProcessOwnerPersonalUseAction;
use App\Actions\Payments\AggregatePaymentPlotsAction;
use App\Actions\Payments\ContractsPaymentsMappingAction;
use App\Actions\Payments\FormattingOwnersDataAction;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use PHPUnit\Framework\TestCase;
use Mockery;

/**
 * Get Owner Payroll Action Test
 *
 * Unit tests for the GetOwnerPayrollAction class.
 * Tests the main orchestration logic of the payroll calculation.
 */
class GetOwnerPayrollActionTest extends TestCase
{
    private GetOwnerPayrollAction $action;
    private $getPaymentsPlotsAction;
    private $processOwnerPersonalUseAction;
    private $aggregatePaymentPlotsAction;
    private $contractsPaymentsMappingAction;
    private $formattingOwnersDataAction;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mocks for all dependencies
        $this->getPaymentsPlotsAction = Mockery::mock(GetPaymentsPlotsAction::class);
        $this->processOwnerPersonalUseAction = Mockery::mock(ProcessOwnerPersonalUseAction::class);
        $this->aggregatePaymentPlotsAction = Mockery::mock(AggregatePaymentPlotsAction::class);
        $this->contractsPaymentsMappingAction = Mockery::mock(ContractsPaymentsMappingAction::class);
        $this->formattingOwnersDataAction = Mockery::mock(FormattingOwnersDataAction::class);

        $this->action = new GetOwnerPayrollAction(
            $this->getPaymentsPlotsAction,
            $this->processOwnerPersonalUseAction,
            $this->aggregatePaymentPlotsAction,
            $this->contractsPaymentsMappingAction,
            $this->formattingOwnersDataAction
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test successful execution of owner payroll calculation
     */
    public function test_execute_successful_payroll_calculation(): void
    {
        $year = 2024;
        $ownerId = 123;
        $path = '1.2.3';
        $filterParams = ['test' => 'value'];

        // Mock the execution flow
        $plotsData = collect([
            ['owner_id' => 123, 'plot_id' => 1, 'area' => 10.5],
            ['owner_id' => 123, 'plot_id' => 2, 'area' => 5.2],
        ]);

        $processedPlots = collect([
            ['owner_id' => 123, 'plot_id' => 1, 'area' => 10.5, 'personal_use' => 1.0],
            ['owner_id' => 123, 'plot_id' => 2, 'area' => 5.2, 'personal_use' => 0.5],
        ]);

        $aggPlots = [
            '123_0' => ['owner_id' => 123, 'total_area' => 15.7, 'total_rent' => 1000.0],
        ];

        $aggPlotsByContract = [
            '456' => ['contract_id' => 456, 'total_area' => 15.7],
        ];

        $mappedPlots = [
            '123_0' => ['owner_id' => 123, 'total_area' => 15.7, 'total_rent' => 1000.0, 'paid_rent' => 500.0],
        ];

        $formattedData = [
            [
                'owner_id' => 123,
                'owner_names' => 'Test Owner',
                'total_area' => '15,700 дка',
                'total_rent' => '1 000,00 лв.',
                'paid_rent' => '500,00 лв.',
            ],
        ];

        // Set up expectations
        $this->getPaymentsPlotsAction
            ->shouldReceive('execute')
            ->once()
            ->with($year, null, null, $ownerId, $path, $filterParams)
            ->andReturn($plotsData);

        $this->processOwnerPersonalUseAction
            ->shouldReceive('execute')
            ->once()
            ->with($plotsData)
            ->andReturn($processedPlots);

        $this->aggregatePaymentPlotsAction
            ->shouldReceive('execute')
            ->twice()
            ->withArgs(function ($plots, $aggBy) {
                return $aggBy === 'rent_type' || $aggBy === 'contract';
            })
            ->andReturn($aggPlots, $aggPlotsByContract);

        $this->contractsPaymentsMappingAction
            ->shouldReceive('execute')
            ->once()
            ->with($aggPlots, $aggPlotsByContract, $year, $ownerId, $path)
            ->andReturn($mappedPlots);

        $this->formattingOwnersDataAction
            ->shouldReceive('execute')
            ->once()
            ->with($mappedPlots)
            ->andReturn($formattedData);

        // Execute the action
        $result = $this->action->execute($year, $ownerId, $path, $filterParams);

        // Assertions
        $this->assertInstanceOf(JsonResponse::class, $result);
        
        $responseData = $result->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($formattedData, $responseData['data']);
        $this->assertEquals($year, $responseData['meta']['year']);
        $this->assertEquals($ownerId, $responseData['meta']['owner_id']);
        $this->assertEquals($path, $responseData['meta']['path']);
        $this->assertEquals(1, $responseData['meta']['total_records']);
    }

    /**
     * Test execution with no_contract_payments filter
     */
    public function test_execute_with_no_contract_payments_filter(): void
    {
        $year = 2024;
        $filterParams = ['no_contract_payments' => true];

        $plotsData = collect([]);
        $processedPlots = collect([]);
        $aggPlots = [];
        $aggPlotsByContract = [];
        $formattedData = [];

        // Set up expectations - contracts payments mapping should NOT be called
        $this->getPaymentsPlotsAction
            ->shouldReceive('execute')
            ->once()
            ->andReturn($plotsData);

        $this->processOwnerPersonalUseAction
            ->shouldReceive('execute')
            ->once()
            ->andReturn($processedPlots);

        $this->aggregatePaymentPlotsAction
            ->shouldReceive('execute')
            ->twice()
            ->andReturn($aggPlots, $aggPlotsByContract);

        $this->contractsPaymentsMappingAction
            ->shouldNotReceive('execute');

        $this->formattingOwnersDataAction
            ->shouldReceive('execute')
            ->once()
            ->with($aggPlots)
            ->andReturn($formattedData);

        // Execute the action
        $result = $this->action->execute($year, null, null, $filterParams);

        // Assertions
        $this->assertInstanceOf(JsonResponse::class, $result);
        $responseData = $result->getData(true);
        $this->assertTrue($responseData['success']);
    }

    /**
     * Test getData method returns array instead of JsonResponse
     */
    public function test_get_data_returns_array(): void
    {
        $year = 2024;
        $formattedData = [
            ['owner_id' => 123, 'owner_names' => 'Test Owner'],
        ];

        // Set up minimal expectations
        $this->getPaymentsPlotsAction
            ->shouldReceive('execute')
            ->once()
            ->andReturn(collect([]));

        $this->processOwnerPersonalUseAction
            ->shouldReceive('execute')
            ->once()
            ->andReturn(collect([]));

        $this->aggregatePaymentPlotsAction
            ->shouldReceive('execute')
            ->twice()
            ->andReturn([], []);

        $this->contractsPaymentsMappingAction
            ->shouldReceive('execute')
            ->once()
            ->andReturn([]);

        $this->formattingOwnersDataAction
            ->shouldReceive('execute')
            ->once()
            ->andReturn($formattedData);

        // Execute getData method
        $result = $this->action->getData($year);

        // Assertions
        $this->assertIsArray($result);
        $this->assertEquals($formattedData, $result);
    }

    /**
     * Test precision settings getter
     */
    public function test_get_precision_settings(): void
    {
        $settings = $this->action->getPrecisionSettings();

        $this->assertIsArray($settings);
        $this->assertArrayHasKey('area', $settings);
        $this->assertArrayHasKey('money', $settings);
        $this->assertArrayHasKey('rent_natura', $settings);
    }

    /**
     * Test execution with minimal parameters
     */
    public function test_execute_with_minimal_parameters(): void
    {
        $year = 2024;

        // Set up minimal expectations
        $this->getPaymentsPlotsAction
            ->shouldReceive('execute')
            ->once()
            ->with($year, null, null, null, null, [])
            ->andReturn(collect([]));

        $this->processOwnerPersonalUseAction
            ->shouldReceive('execute')
            ->once()
            ->andReturn(collect([]));

        $this->aggregatePaymentPlotsAction
            ->shouldReceive('execute')
            ->twice()
            ->andReturn([], []);

        $this->contractsPaymentsMappingAction
            ->shouldReceive('execute')
            ->once()
            ->andReturn([]);

        $this->formattingOwnersDataAction
            ->shouldReceive('execute')
            ->once()
            ->andReturn([]);

        // Execute the action
        $result = $this->action->execute($year);

        // Assertions
        $this->assertInstanceOf(JsonResponse::class, $result);
        $responseData = $result->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($year, $responseData['meta']['year']);
        $this->assertNull($responseData['meta']['owner_id']);
        $this->assertNull($responseData['meta']['path']);
    }
}
