<?php

declare(strict_types=1);

namespace Tests\Unit\Utils;

use App\Utils\Math;
use PHPUnit\Framework\TestCase;

/**
 * Math Utility Test
 *
 * Unit tests for the Math utility class.
 * Tests precision mathematical operations.
 */
class MathTest extends TestCase
{
    /**
     * Test addition with precision
     */
    public function test_addition_with_precision(): void
    {
        $result = Math::add(1.234, 2.567, 2);
        $this->assertEquals(3.80, $result);

        $result = Math::add(0.1, 0.2, 2);
        $this->assertEquals(0.30, $result);
    }

    /**
     * Test subtraction with precision
     */
    public function test_subtraction_with_precision(): void
    {
        $result = Math::sub(5.678, 2.345, 2);
        $this->assertEquals(3.33, $result);

        $result = Math::sub(1.0, 0.9, 2);
        $this->assertEquals(0.10, $result);
    }

    /**
     * Test multiplication with precision
     */
    public function test_multiplication_with_precision(): void
    {
        $result = Math::mul(2.5, 3.2, 2);
        $this->assertEquals(8.00, $result);

        $result = Math::mul(0.1, 0.3, 3);
        $this->assertEquals(0.030, $result);
    }

    /**
     * Test division with precision
     */
    public function test_division_with_precision(): void
    {
        $result = Math::div(10, 3, 2);
        $this->assertEquals(3.33, $result);

        $result = Math::div(1, 3, 4);
        $this->assertEquals(0.3333, $result);
    }

    /**
     * Test division by zero throws exception
     */
    public function test_division_by_zero_throws_exception(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Division by zero');

        Math::div(10, 0);
    }

    /**
     * Test rounding
     */
    public function test_rounding(): void
    {
        $result = Math::round(3.14159, 2);
        $this->assertEquals(3.14, $result);

        $result = Math::round(3.146, 2);
        $this->assertEquals(3.15, $result);

        $result = Math::round(-3.146, 2);
        $this->assertEquals(-3.15, $result);
    }

    /**
     * Test comparison
     */
    public function test_comparison(): void
    {
        $this->assertEquals(0, Math::compare(1.0, 1.0));
        $this->assertEquals(1, Math::compare(2.0, 1.0));
        $this->assertEquals(-1, Math::compare(1.0, 2.0));
    }

    /**
     * Test equals
     */
    public function test_equals(): void
    {
        $this->assertTrue(Math::equals(1.0, 1.0));
        $this->assertFalse(Math::equals(1.0, 1.1));
        $this->assertTrue(Math::equals(1.234, 1.235, 2));
    }

    /**
     * Test absolute value
     */
    public function test_absolute_value(): void
    {
        $this->assertEquals(5.0, Math::abs(-5.0));
        $this->assertEquals(5.0, Math::abs(5.0));
        $this->assertEquals(0.0, Math::abs(0.0));
    }

    /**
     * Test maximum
     */
    public function test_maximum(): void
    {
        $this->assertEquals(5.0, Math::max(3.0, 5.0));
        $this->assertEquals(5.0, Math::max(5.0, 3.0));
        $this->assertEquals(5.0, Math::max(5.0, 5.0));
    }

    /**
     * Test minimum
     */
    public function test_minimum(): void
    {
        $this->assertEquals(3.0, Math::min(3.0, 5.0));
        $this->assertEquals(3.0, Math::min(5.0, 3.0));
        $this->assertEquals(5.0, Math::min(5.0, 5.0));
    }

    /**
     * Test sum array
     */
    public function test_sum_array(): void
    {
        $values = [1.1, 2.2, 3.3];
        $result = Math::sum($values, 2);
        $this->assertEquals(6.60, $result);

        $values = [];
        $result = Math::sum($values);
        $this->assertEquals(0.0, $result);
    }

    /**
     * Test percentage calculation
     */
    public function test_percentage(): void
    {
        $result = Math::percentage(100, 25, 2);
        $this->assertEquals(25.00, $result);

        $result = Math::percentage(200, 12.5, 2);
        $this->assertEquals(25.00, $result);
    }

    /**
     * Test null value handling
     */
    public function test_null_value_handling(): void
    {
        $this->assertEquals(5.0, Math::add(null, 5.0));
        $this->assertEquals(5.0, Math::add(5.0, null));
        $this->assertEquals(0.0, Math::add(null, null));
    }

    /**
     * Test string value handling
     */
    public function test_string_value_handling(): void
    {
        $this->assertEquals(7.5, Math::add('2.5', '5.0'));
        $this->assertEquals(5.0, Math::add('', 5.0));
        $this->assertEquals(5.0, Math::add('-', 5.0));
    }

    /**
     * Test high precision calculations
     */
    public function test_high_precision_calculations(): void
    {
        $result = Math::add(0.123456789, 0.987654321, 8);
        $this->assertEquals(1.11111111, $result);

        $result = Math::div(1, 7, 10);
        $this->assertEquals(0.1428571428, $result);
    }

    /**
     * Test area precision (3 decimal places)
     */
    public function test_area_precision(): void
    {
        $result = Math::add(12.3456, 7.8901, 3);
        $this->assertEquals(20.235, $result);
    }

    /**
     * Test money precision (2 decimal places)
     */
    public function test_money_precision(): void
    {
        $result = Math::mul(123.456, 2.789, 2);
        $this->assertEquals(344.31, $result);
    }

    /**
     * Test large number handling
     */
    public function test_large_number_handling(): void
    {
        $result = Math::add(999999.99, 0.01, 2);
        $this->assertEquals(1000000.00, $result);

        $result = Math::mul(1000000, 1.5, 2);
        $this->assertEquals(1500000.00, $result);
    }
}
