<?php

declare(strict_types=1);

namespace Tests\Unit\Utils;

use App\Utils\PayrollHelper;
use Carbon\Carbon;
use Tests\TestCase;

/**
 * Payroll Helper Test
 *
 * Unit tests for the PayrollHelper utility class.
 * Tests helper functions for payroll calculations.
 */
class PayrollHelperTest extends TestCase
{
    /**
     * Test farming year calculation from date
     */
    public function test_farming_year_from_date(): void
    {
        // Test date in farming year 2023 (before October 1st)
        $result = PayrollHelper::getFarmingYearFromDate('2024-05-15');
        $this->assertEquals(2023, $result['id']);
        $this->assertEquals('2023/2024', $result['name']);

        // Test date in farming year 2024 (after October 1st)
        $result = PayrollHelper::getFarmingYearFromDate('2024-10-15');
        $this->assertEquals(2024, $result['id']);
        $this->assertEquals('2024/2025', $result['name']);

        // Test exact October 1st
        $result = PayrollHelper::getFarmingYearFromDate('2024-10-01');
        $this->assertEquals(2024, $result['id']);
    }

    /**
     * Test farming year with Carbon instance
     */
    public function test_farming_year_with_carbon(): void
    {
        $date = Carbon::create(2024, 3, 15);
        $result = PayrollHelper::getFarmingYearFromDate($date);
        
        $this->assertEquals(2023, $result['id']);
        $this->assertInstanceOf(Carbon::class, $result['start_date']);
        $this->assertInstanceOf(Carbon::class, $result['end_date']);
    }

    /**
     * Test BGN to EUR conversion
     */
    public function test_bgn_to_euro_conversion(): void
    {
        $result = PayrollHelper::bgnToEuro(195.583);
        $this->assertEquals(100.00, $result);

        $result = PayrollHelper::bgnToEuro(100, 2.0);
        $this->assertEquals(50.00, $result);
    }

    /**
     * Test currency formatting
     */
    public function test_currency_formatting(): void
    {
        $result = PayrollHelper::formatCurrency(1234.56);
        $this->assertEquals('1 234,56 лв.', $result);

        $result = PayrollHelper::formatCurrency(0);
        $this->assertEquals('0,00 лв.', $result);
    }

    /**
     * Test area formatting
     */
    public function test_area_formatting(): void
    {
        $result = PayrollHelper::formatArea(12.345);
        $this->assertEquals('12,345 дка', $result);

        $result = PayrollHelper::formatArea(0);
        $this->assertEquals('0,000 дка', $result);
    }

    /**
     * Test owner path key generation
     */
    public function test_owner_path_key_generation(): void
    {
        $result = PayrollHelper::generateOwnerPathKey(123);
        $this->assertEquals('123_0', $result);

        $result = PayrollHelper::generateOwnerPathKey(123, '1.2.3');
        $this->assertEquals('123_1.2.3', $result);
    }

    /**
     * Test owner path key parsing
     */
    public function test_owner_path_key_parsing(): void
    {
        $result = PayrollHelper::parseOwnerPathKey('123_0');
        $this->assertEquals(['owner_id' => 123, 'path' => '0'], $result);

        $result = PayrollHelper::parseOwnerPathKey('456_1.2.3');
        $this->assertEquals(['owner_id' => 456, 'path' => '1.2.3'], $result);
    }

    /**
     * Test dead in current farming year check
     */
    public function test_dead_in_current_farming_year(): void
    {
        // Test owner who died in current farming year
        $result = PayrollHelper::isDeadInCurrentFarmingYear('2024-05-15', 2023);
        $this->assertTrue($result);

        // Test owner who died in different farming year
        $result = PayrollHelper::isDeadInCurrentFarmingYear('2024-05-15', 2024);
        $this->assertFalse($result);

        // Test null death date
        $result = PayrollHelper::isDeadInCurrentFarmingYear(null, 2023);
        $this->assertFalse($result);
    }

    /**
     * Test UUID generation
     */
    public function test_uuid_generation(): void
    {
        $result = PayrollHelper::generateUuid('test_input');
        $this->assertEquals(6, strlen($result));
        $this->assertIsString($result);

        // Same input should produce same UUID
        $result2 = PayrollHelper::generateUuid('test_input');
        $this->assertEquals($result, $result2);
    }

    /**
     * Test path building
     */
    public function test_path_building(): void
    {
        $result = PayrollHelper::buildPath(null, 123);
        $this->assertEquals('123', $result);

        $result = PayrollHelper::buildPath('0', 123);
        $this->assertEquals('123', $result);

        $result = PayrollHelper::buildPath('1.2', 3);
        $this->assertEquals('1.2.3', $result);
    }

    /**
     * Test path parsing
     */
    public function test_path_parsing(): void
    {
        $result = PayrollHelper::parsePath('1.2.3');
        $this->assertEquals([1, 2, 3], $result);

        $result = PayrollHelper::parsePath('0');
        $this->assertEquals([], $result);

        $result = PayrollHelper::parsePath('');
        $this->assertEquals([], $result);
    }

    /**
     * Test path contains owner check
     */
    public function test_path_contains_owner(): void
    {
        $this->assertTrue(PayrollHelper::pathContainsOwner('1.2.3', 2));
        $this->assertFalse(PayrollHelper::pathContainsOwner('1.2.3', 4));
        $this->assertFalse(PayrollHelper::pathContainsOwner('0', 1));
    }

    /**
     * Test path depth calculation
     */
    public function test_path_depth(): void
    {
        $this->assertEquals(3, PayrollHelper::getPathDepth('1.2.3'));
        $this->assertEquals(1, PayrollHelper::getPathDepth('1'));
        $this->assertEquals(0, PayrollHelper::getPathDepth('0'));
        $this->assertEquals(0, PayrollHelper::getPathDepth(''));
    }

    /**
     * Test precision validation
     */
    public function test_precision_validation(): void
    {
        $this->assertEquals(2, PayrollHelper::validatePrecision(2));
        $this->assertEquals(0, PayrollHelper::validatePrecision(-1));
        $this->assertEquals(16, PayrollHelper::validatePrecision(20));
    }
}
