<?php

namespace TF\Commands\Common;

use DateTime;
use Exception;
use PDO;
use PHPExcel;
use PHPExcel_IOFactory;
use PHPExcel_Reader_Excel2007;
use PHPExcel_Reader_Exception;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;

/**
 * Class CreateContractsTableCommand.
 *
 * @property PDO $mainPDO
 * @property PDO $userPDO
 * @property InputInterface $input
 * @property OutputInterface $output
 * @property PHPExcel_Reader_Excel2007 $excelReader
 * @property PHPExcel $excelWorkbook
 * @property array $excelData
 */
class CreateContractsTableCommand extends BaseCommand
{
    public const MAX_YEAR_DATE = '2037';

    public const STATUS_SUCCESS = 0;
    public const ERROR_USER_NOT_FOUND = 1;
    public const ERROR_INVALID_FILE_PATH = 2;
    public const ERROR_READING_FILE = 3;

    // Sheet's names
    public $valSheetNamePref = ['аренда_наем', 'собствени'];

    // Columns mapper. Key - date base column's name, value - name of the column in Excel file
    public $fields = [
        [
            'katident' => ['имот', 'Идентификатор', 'Номер на имота', '№ имот', 'Кадидент'],
            'mestnost' => ['Местност'],
            'land' => ['землище', 'Землище'],
            'ekatte' => ['EKATTE', 'екатте'],
            'area_type' => ['НТП', 'НТП код'],
            'c_num' => ['договор', 'Договор №', 'Номер на договор/анекс', 'вътрешен номер на договор'],
            'c_date' => ['Датата на сключване', 'дата на договор', 'Дата на сключване', 'Дата на закупуване', 'дата на закупуване'],
            'start_date' => ['Начална дата', 'От дата', 'начална дата'],
            'due_date' => ['Крайна дата', 'До дата'],
            'nm_usage_rights' => ['Вид договор', 'Ползване', 'Тип на договора', 'Вид на договор'],
            'owner_farming' => ['Стопанство', 'Стопанство (фирма)', 'собственик (стопанство)'],
            'document_area' => ['площ по документ', 'дка по документ', 'Площ(дка)', 'Площ по документ'],
            'contract_area' => ['площ по договор', 'Площ на имота по договор (дка)', 'Площ(дка)', 'Площ по договор'],
            'category' => ['категория', 'Категория'],
            'owner_name' => ['Име собственик'],
            'owner_surname' => ['Презиме собственик'],
            'familly' => ['Фамилия собственик'],
            'egn_eik' => ['ЕГН/ЕИК собственик', 'ЕГН/ЕИК  собственик', 'ЕГН/ЛНЧ/ЕИК'],
            'owner_type' => ['Вид собственик', 'Тип сбственик'],
            'city' => ['Град собственик'],
            'address' => ['Адрес собственик', 'Адрес'],
            'owner_address' => null,
            'phone' => ['Телефон собственик', 'Телефон'],
            'rep_name' => ['Име представител'],
            'rep_surname' => ['Презиме представител'],
            'rep_familly' => ['Фамилия представител'],
            'rep_egn' => ['ЕГН представител'],
            'sv_num' => ['Номер вписване'],
            'sv_date' => ['Дата на вписване'],
            'renta_nat' => ['Рента в натура / дка', 'Рента в натура/дка'],
            'renta_nat_type' => ['Тип натура'],
            'nat_unit_value' => ['Тип(мерна единица)', 'Тип (мерна единица)', 'Мерна единица за натура'],
            'renta' => ['Рента лв ./дка', 'Рента (лв.)', 'Рента лв./дка'],
            'payday' => ['Падеж за рентното плащане'],
            'comment' => ['забележка договор', 'Забележка'],
            'tom' => ['том', 'Том'],
            'delo' => ['дело', 'Дело'],
            'na_num' => ['Номер нотариален акт', 'Нотариален акт №'],
            'price_per_acre' => ['Цена на придобиване (за дка)'],
        ],
    ];
    protected $mainPDO;
    protected $userPDO;
    protected $tableName;

    protected $errors = [];

    protected $excelReader;
    protected $excelWorkbook;
    protected $excelData;
    protected $workingDir;

    private $user;
    private $userId;
    private $userDb;
    private $userName;

    public function __construct()
    {
        parent::__construct();
        $main_dsn = 'pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';';
        $this->mainPDO = new PDO($main_dsn, DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
    }

    public function setUser($user)
    {
        if (is_int((int)$user)) {
            $user_db_where = 'id = ' . $user;
        } elseif (false !== strpos($user, 'db')) {
            $user_db_where = "database = '{$user}'";
        } else {
            $user_db_where = "username = '{$user}'";
        }
        $statement = $this->mainPDO->prepare("SELECT id, username, database FROM su_users u WHERE ({$user_db_where})");
        $statement->execute();
        $users = $statement->fetchAll(PDO::FETCH_ASSOC);
        if (empty($users)) {
            $this->output->writeln('Error: No user found with ' . $user_db_where);

            return false;
        }
        if (count($users) > 1) {
            $this->output->writeln('Error: Too many user found with ' . $user_db_where);

            return false;
        }
        $this->user = $users[0];
        $this->userId = $this->user['id'];
        $this->userName = $this->user['username'];
        $this->userDb = $this->user['database'];
        $user_dns = 'pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $this->userDb . ';';
        $this->userPDO = new PDO($user_dns, DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        return $this->user;
    }

    public function createImpTable($importTableName = 'import_rents', $excel_columns = [])
    {
        $this->tableName = $importTableName;
        $start_schema = [
            'id' => '"id" SERIAL NOT NULL PRIMARY KEY',
            'katident' => '"katident" varchar(255) COLLATE "default" ',
            'c_num' => '"c_num" varchar(255) COLLATE "default" ',
            'c_date' => '"c_date" date ',
            'nm_usage_rights' => '"nm_usage_rights" varchar(255) COLLATE "default" ',
            'owner_farming' => '"owner_farming" varchar(255) COLLATE "default" ',
            'contract_area' => '"contract_area" numeric(10,4) ',
            'document_area' => '"document_area" numeric(10,4) ',
            'ekatte' => '"ekatte" varchar(255) COLLATE "default" ',
            'area_type' => '"area_type" varchar(255) COLLATE "default" ',
            'mestnost' => '"mestnost" varchar(255) COLLATE "default" ',
            'land' => '"land" varchar(255) COLLATE "default" ',
            'category' => '"category" varchar(255) COLLATE "default" ',
            'owner_name' => '"owner_name" varchar(255) COLLATE "default" ',
            'owner_surname' => '"owner_surname" varchar(255) COLLATE "default" ',
            'familly' => '"familly" varchar(255) COLLATE "default" ',
            'egn_eik' => '"egn_eik" varchar(255) COLLATE "default" ',
            'owner_type' => '"owner_type" varchar(255) COLLATE "default" ',
            'city' => '"city" varchar(255) COLLATE "default" ',
            'address' => '"address" varchar(255) COLLATE "default" ',
            'owner_address' => '"owner_address" varchar(255) COLLATE "default" ',
            'phone' => '"phone" varchar(255) COLLATE "default" ',
            'rep_name' => '"rep_name" varchar(255) COLLATE "default" ',
            'rep_surname' => '"rep_surname" varchar(255) COLLATE "default" ',
            'rep_familly' => '"rep_familly" varchar(255) COLLATE "default" ',
            'rep_egn' => '"rep_egn" varchar(255) COLLATE "default" ',
            'start_date' => '"start_date" date ',
            'due_date' => '"due_date" date ',
            'renta_nat' => '"renta_nat" varchar(255) COLLATE "default" ',
            'renta_nat_type' => '"renta_nat_type" varchar(255) COLLATE "default" ',
            'nat_unit_value' => '"nat_unit_value" varchar(255) COLLATE "default" ',
            'renta' => '"renta" float8 ',
            'payday' => '"payday" varchar(255) COLLATE "default" ',
            'comment' => '"comment" varchar(255) COLLATE "default" ',
            'tom' => '"tom" varchar(255) COLLATE "default" ',
            'delo' => '"delo" varchar(255) COLLATE "default" ',
            'na_num' => '"na_num" varchar(255) COLLATE "default" ',
            'sv_num' => '"sv_num" varchar(255) COLLATE "default" ',
            'sv_date' => '"sv_date" timestamp(6) ',
            'price_per_acre' => '"price_per_acre" numeric(10,6) ',
        ];

        if (!empty($excel_columns)) {
            $excel_columns = array_combine(array_values($excel_columns), $excel_columns);
            $create_columns = array_intersect_key($start_schema, $excel_columns);
            if (!empty($create_columns)) {
                $create_columns = array_merge(['id' => $start_schema['id']], $create_columns);
            }
        }

        if (empty($excel_columns)) {
            $create_columns = $start_schema;
        }

        $table_sql = "DROP TABLE IF EXISTS \"public\".\"{$importTableName}\";";
        $this->userPDO->query($table_sql);
        $table_sql = "CREATE TABLE \"public\".\"{$importTableName}\" (";
        $table_sql .= implode(',', $create_columns);
        $table_sql .= ' );';
        $result = $this->userPDO->query($table_sql);

        if (empty($result)) {
            echo '====== ERROR DURING INSERTING DATA (method: createImpTable) ======>>>><pre>' . print_r($this->userPDO->errorInfo(), true) . '</pre><<<<====';
            exit;
        }

        return $result;
    }

    public function insertData($data, $columns)
    {
        $columns = implode(',', $columns);
        $insert_sql = "INSERT INTO public.{$this->tableName} ({$columns}) VALUES ";
        foreach ($data as $row) {
            $temp_sql = '(';
            foreach ($row as $key => $value) {
                $temp_sql .= '' !== $value ? "'{$value}'" : 'NULL';
                $temp_sql .= ',';
            }
            $temp_sql = rtrim($temp_sql, ',');
            $temp_sql .= '),';
            $insert_sql .= $temp_sql;
        }
        $insert_sql = rtrim($insert_sql, ',' . PHP_EOL);
        $result = $this->userPDO->query($insert_sql);

        if (empty($result)) {
            echo '====== ERROR DURING INSERTING DATA (method: insertData) ======>>>><pre>' . print_r($this->userPDO->errorInfo(), true) . '</pre><<<<====';
            exit;
        }

        return $result;
    }

    protected function configure()
    {
        $this->setName('tf:create_import_table')
            ->setDescription('Create import table for contracts by reading client \'s excel file')
            ->addArgument('user', InputArgument::REQUIRED, 'User')
            ->addArgument('excel_file_path', InputArgument::REQUIRED, 'The excel file path')
            ->addArgument('excel_sheet_index_number', InputArgument::OPTIONAL, 'The sheet to load specified by index (starting from zero)');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_db = $input->getArgument('user');
        $file_path = $input->getArgument('excel_file_path');
        if (0 === strpos($file_path, '?', 0)) {
            $file_path = str_replace('?', '', $file_path);
        }
        if (!file_exists($file_path)) {
            $this->errorImportLog('File ' . $file_path . '  not found, exiting.');

            return self::ERROR_INVALID_FILE_PATH;
        }
        $sheet_index = $input->getArgument('excel_sheet_index_number');
        $helper = $this->getHelper('question');
        $this->input = $input;
        $this->output = $output;
        if (empty($user_db)) {
            $question = new Question('Please Specify an user, either by id or by username or by db_name:' . PHP_EOL);
            $answer = $helper->ask($input, $output, $question);
            if (!$answer) {
                $this->errorImportLog('No user specified, exiting');

                return self::ERROR_USER_NOT_FOUND;
            }
            $user_db = $answer;
        }

        $this->setUser($user_db);
        if (!$this->user) {
            $this->errorImportLog('user not found, exiting.');

            return self::ERROR_USER_NOT_FOUND;
        }
        $this->errorImportLog('Info: found user ' . $this->userName);
        $this->workingDir = basename($file_path);

        $sheets_to_import = [];

        try {
            $this->excelWorkbook = $this->getWorkBook($file_path);
            if (!is_numeric($sheet_index)) {
                $sheets_to_import = $this->getImportableSheets($this->excelWorkbook->getSheetNames());
            } else {
                $sheets_to_import[] = $this->excelWorkbook->getSheet((int)$sheet_index)->getTitle();
            }
        } catch (Exception $exception) {
            $this->errorImportLog('Error reading excel file: ' . $exception->getMessage(), __METHOD__);

            return self::ERROR_READING_FILE;
        }

        $excel_data = [];
        $return_data = [];
        // rewrite all the lookup fields in lower case
        $this->setFieldsLowCase();

        foreach ($sheets_to_import as $sheet_index => $sheet) {
            $excel_data[$sheet]['rows'] = $this->excelWorkbook->setActiveSheetIndexByName($sheet)->toArray(null, true, true, true);
            $excel_data[$sheet]['file_headers'] = $excel_data[$sheet]['rows'][1];
            $excel_data[$sheet]['valid_headers'] = $this->collectHeaders($excel_data[$sheet]['file_headers']);
            $temp = $this->collectData($excel_data[$sheet]);
            $valid = $this->checkRequiredHeaders(array_keys($temp[0]), $sheet);
            if (!$valid) {
                $this->errorImportLog($this->errors);
                $this->errorImportLog('Skipping Import for sheet ' . $sheet);

                continue;
            }
            $excel_data[$sheet]['rows'] = $temp;
            $columns = array_keys($excel_data[$sheet]['rows'][0]);
            $this->createImpTable('import_' . $sheet_index, $columns);
            $this->insertData($excel_data[$sheet]['rows'], $columns);
            $return_data[$sheet] = $excel_data[$sheet];
            $this->errorImportLog('Data found: ' . print_r($excel_data[$sheet]['rows'], true) . ' rows loaded: ' . count($excel_data[$sheet]['rows']), __METHOD__);
        }
        $this->errorImportLog('Process Ended: table crated: ' . $this->tableName);

        return self::STATUS_SUCCESS;
    }

    /**
     * @throws PHPExcel_Reader_Exception
     */
    protected function getWorkBook($file_path)
    {
        $this->errorImportLog('readExcelFile process started', __METHOD__);
        $this->errorImportLog('File Path: ' . $file_path, __METHOD__);
        $file_type = PHPExcel_IOFactory::identify($file_path);
        $this->excelReader = PHPExcel_IOFactory::createReader($file_type);
        $this->excelReader->setReadDataOnly(true);

        return $this->excelReader->load($file_path);
    }

    protected function checkRequiredHeaders($valid_headers, $sheet)
    {
        $this->errors = [];
        $required_fields = ['c_num', 'katident', 'owner_farming'];
        foreach ($required_fields as $index => $header) {
            if (!in_array($header, $valid_headers, false)) {
                $this->errors[] = 'Missing Column: ' . $header . ' in excel Sheet: ' . $sheet;
            }
        }

        return empty($this->errors);
    }

    protected function collectHeaders($file_headers)
    {
        $return = [];
        foreach ($file_headers as $coordinate => $header) {
            foreach ($this->fields[0] as $column_name => $fields_arr) {
                if (empty($fields_arr)) {
                    continue;
                }

                $excel_header = mb_strtolower(trim($header), 'UTF-8');
                $excel_header = str_replace('  ', ' ', $excel_header);

                if (in_array($excel_header, $fields_arr, false)) {
                    $field_index = array_search($excel_header, $fields_arr, false);
                    $dest_field = $fields_arr[$field_index];
                    if (!$dest_field) {
                        continue;
                    }
                    $return[$coordinate] = $column_name;
                }
            }
        }

        return $return;
    }

    protected function collectData($data)
    {
        array_shift($data['rows']);
        $columns = [];

        foreach ($data['valid_headers'] as $coordinate => $column) {
            $row_values = array_column($data['rows'], $coordinate);
            if (array_unique($row_values) == [null]) {
                continue;
            }
            $columns[$column] = $row_values;
        }

        $return = [];

        // transpose columns to associative array
        foreach ($columns as $column => $row_values) {
            foreach ($row_values as $index => $value) {
                if (!empty($value) && false !== strpos($column, '_date', 0)) {
                    if (is_numeric($value)) {
                        $date_time = new DateTime("1899-12-30 + {$value} days");
                    } else {
                        $date_time = new DateTime($value);
                    }
                    $value = $date_time->format('Y-m-d H:i:s');
                }
                $return[$index][$column] = null !== $value ? trim($value) : null;
            }
        }

        return $return;
    }

    protected function setFieldsLowCase()
    {
        foreach ($this->fields as $key => $fields) {
            foreach ($fields as $column => $field_arr) {
                if (empty($field_arr)) {
                    continue;
                }

                foreach ($field_arr as $index => $field) {
                    $this->fields[$key][$column][$index] = mb_strtolower(trim($field), 'UTF-8');
                }
            }
        }
    }

    /**
     * @param array $sheets
     *
     * @return array
     */
    private function getImportableSheets($sheets)
    {
        $result = [];
        foreach ($sheets as $index => $sheet) {
            if (in_array($sheet, $this->valSheetNamePref)) {
                $result[] = $sheet;
            }
        }

        return $result;
    }

    private function errorImportLog($message, $method = null)
    {
        if (!$this->output) {
            return;
        }
        if (is_array($message)) {
            $message = print_r($message, true);
        }
        if (null !== $method) {
            $output = 'method: ' . $method . ' message: ' . $message;
        } else {
            $output = 'message: ' . $message;
        }
        $this->output->writeln($output . PHP_EOL);
    }
}
