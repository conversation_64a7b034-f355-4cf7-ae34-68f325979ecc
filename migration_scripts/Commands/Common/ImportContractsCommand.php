<?php

namespace TF\Commands\Common;

use DateTime;
use Exception;
use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;
use TF\Application\Common\Config;

/**
 * Class ImportContractsCommand.
 *
 * @property PDO $mainPDO
 * @property PDO $userPDO
 * @property InputInterface $input
 * @property OutputInterface $output
 */
class ImportContractsCommand extends BaseCommand
{
    public const MAX_YEAR_DATE = '2037';

    public const STATUS_SUCCESS = 0;
    public const ERROR_USER_NOT_FOUND = 1;
    public const ERROR_TABLE_NOT_FOUND = 2;
    public const ERROR_INVALID_DATA = 3;
    public const ERROR_MISSING_USER_FARM = 4;
    public const ERROR_NO_CONTRACTS_TO_LOAD = 5;

    public $forceRun = false;
    public $truncateTables = false;
    public $updateKvs = false;
    public $missingPlots = [];

    public $validContracts = false;
    public $validCDates = false;
    public $validFarmName = false;
    public $validPlots = false;
    public $validCType = false;
    public $validOwnersEgns = false;
    public $validOwnersTypes = false;
    protected $mainPDO;
    protected $userPDO;

    protected $farmings;
    protected $contractsToInsert;

    protected $input;
    protected $output;

    protected $contractTypes = [
        'собственост' => 1,
        'Собствен' => 1,
        'аренда' => 2,
        'аренд' => 2,
        'Аренден договор' => 2,
        'наем' => 3,
        'Наем' => 3,
        'споразумение' => 4,
        'съвместна обработка' => 5,
    ];

    private $user;
    private $userId;
    private $userName;
    private $userDb;
    private $stubTable;

    private $logPath;

    private $index;
    private $contractsCount;

    public function __construct()
    {
        parent::__construct();
        $main_dsn = 'pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';';
        $this->mainPDO = new PDO($main_dsn, DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $this->logPath = SITE_PATH . 'logs/contract_import_log.txt';
        $this->resetLog();
    }

    /**
     * @param array $contracts
     */
    public function importContracts($contracts)
    {
        $this->contractsCount = count($contracts);
        $this->errorImportLog('UNIQUE CONTRACTS FOUND TO IMPORT: ' . $this->contractsCount . ' CONTRACTS.');
        foreach ($contracts as $index => &$contract) {
            $this->index = $index;
            if (empty($contract['c_num']) || !isset($contract['c_num'])) {
                $this->logInvalidContracts($contract, $index);

                continue;
            }
            $contract['owner_farming_id'] = $this->setClientFarmId($contract['owner_farming']);
            if (!$contract['owner_farming_id']) {
                $this->logInvalidContracts($contract);

                continue;
            }
            $contract['orig_usage_rights'] = $contract['nm_usage_rights'];
            $contract['nm_usage_rights'] = $this->setContractType($contract['nm_usage_rights']);

            if (!empty($contract['renta_nat_type'])) {
                $rentaType = $this->getRentаTypeByName($contract['renta_nat_type']);
                $contract['renta_nat_type_id'] = $rentaType['id'];
            }

            if (!$contract['nm_usage_rights']) {
                $this->logInvalidContracts($contract);

                continue;
            }
            unset($contract['orig_usage_rights']);

            $contract = $this->setCDefaults($contract);
            if (!$this->logInvalidContracts($contract)) {
                continue;
            }

            $existing = $this->selectContract($contract);
            if ($existing) {
                $contract = $existing;
                $contract['id'] = $existing['id'];
                unset($existing);
            } else {
                $contract['id'] = $this->insertContract($contract);
            }
            $insertedContracts[] = $contract['id'];

            if (!empty($contract['renta_nat_type_id'])) {
                $this->insertContractRentaNat($contract); // insert rent natura in contracts_rents
            }

            $plots = $this->selectContractsPlots($contract);

            $this->errorImportLog('Contract: ' . $contract['c_num'] . ' has ' . count($plots) . ' plots: ' . implode(',', array_column($plots, 'katident')));
            $this->importPlots($contract, $plots);

            $contract['plots'] = $plots;
        }
        $this->contractsToInsert = $contracts;
    }

    public function importPlots($contract, $plots)
    {
        $plots_count = count($plots);
        foreach ($plots as $plot_index => $plot) {
            $this->errorImportLog('status: Contract number: (' . ($this->index + 1) . ' of ' . $this->contractsCount . ') num: ' . $plot['c_num'] . ' on plot ' . $plot['katident'] . ' - (plot ' . ($plot_index + 1) . ' of ' . $plots_count . ')');

            if (null == $plot['gid'] || empty($plot['gid'])) {
                $this->errorImportLog('ERR - Plot not existing in kvs_layer; katident: ' . $plot['katident'] . ' contract_num: ' . $plot['c_num']);

                continue;
            }

            $plot['owner_farming_id'] = $contract['owner_farming_id'];
            $plot['nm_usage_rights'] = $this->setContractType($plot['nm_usage_rights']);
            $plot = $this->setCDefaults($plot);
            $plot = $this->selectSumContractsArea($plot);
            // relation between plot and contract, one to one
            $contractsPlotsRelId = $this->selectContractPlotRel($plot, $contract['id']);
            if (null == $contractsPlotsRelId) {
                // no contract relation ,  insert data into su_contracts_plots_rel table
                if (Config::CONTRACT_TYPE_OWN == $plot['nm_usage_rights']) {
                    // if owner contract type
                    $contractsPlotsRelId = $this->insertOwnershipContractsPlotsRel($contract['id'], $plot);
                } else {
                    // if rent contract type
                    $contractsPlotsRelId = $this->insertRentContractsPlotsRel($contract['id'], $plot);
                }
            } else {
                $this->updateContractPlotRel($plot, $contractsPlotsRelId);
            }
            $insertedContractsPlotsRel[] = $contractsPlotsRelId;

            if (Config::CONTRACT_TYPE_OWN != $plot['nm_usage_rights']) {
                $owner_id = $this->getOrCreateOwner($plot, false);
                if (null !== $owner_id) {
                    $rep_id = $this->getOrCreateRep($plot, $owner_id);
                    $insertedOwners[] = $owner_id;
                    $insertedOwnersReps[] = $rep_id;
                    $plot = $this->setOwnerPercent($plot);
                    $plotsParams = [
                        'pc_rel_id' => $contractsPlotsRelId,
                        'numerator' => round($plot['numerator'], 0),
                        'denominator' => round($plot['denominator'], 0),
                        'owner_percent' => $plot['owner_percent'],
                        'owner_id' => $owner_id,
                        'rep_id' => (false == $rep_id) ? null : $rep_id,
                    ];

                    $plotOwnersRelId = $this->selectPlotOwnersRelation($plotsParams);
                    if (null == $plotOwnersRelId) {
                        $plotOwnersRelId = $this->insertPlotsOwnersRel($plotsParams); // insert data into contract_plots table
                        $insertedPlotsOwnersRel[] = $plotOwnersRelId;
                    }
                }
            } else {
                $hypothec_id1 = null;
                $hypothec_id2 = null;
                if (!empty($plot['first_hypothec'])) {
                    $hypothec_id1 = $this->selectHypothec($plot['first_hypothec']);
                    if (!$hypothec_id1) {
                        $hypothec_id1 = $this->insertHypothec($plot['first_hypothec']);
                    }
                }
                if (!empty($plot['second_hypothec'])) {
                    $hypothec_id2 = $this->selectHypothec($plot['second_hypothec']);
                    if (!$hypothec_id2) {
                        $hypothec_id2 = $this->insertHypothec($plot['second_hypothec']);
                    }
                }
                if (!empty($plot['first_hypothec_num'])) {
                    $hypothec_rel_id = $this->selectHypothecPlotRel($plot['gid'], $hypothec_id1, $plot['first_hypothec_num']);
                    if (null === $hypothec_rel_id) {
                        $this->insertHypothecPlotRel(
                            [
                                'plot_id' => $plot['gid'],
                                'hypothec_id' => $hypothec_id1,
                                'hypothec_area' => $plot['contract_area'],
                            ]
                        );
                    }
                }
                if (!empty($plot['second_hypothec_num'])) {
                    $hypothec_rel_id = $this->selectHypothecPlotRel($plot['gid'], $hypothec_id2, $plot['second_hypothec_num']);
                    if (null === $hypothec_rel_id) {
                        $this->insertHypothecPlotRel(
                            [
                                'plot_id' => $plot['gid'],
                                'hypothec_id' => $hypothec_id2,
                                'hypothec_area' => $plot['contract_area'],
                            ]
                        );
                    }
                }
            }

            if ($this->updateKvs) {
                if (empty($plot['kvs_cat']) && !empty($plot['category']) && is_int($plot['category'])) {
                    $this->updateKvs(['gid' => $plot['gid'], 'field' => 'category', 'value' => $plot['category']]);
                }
                if (empty($plot['kvs_mest']) && !empty($plot['mestnost']) && is_int($plot['mestnost'])) {
                    $this->updateKvs(['gid' => $plot['gid'], 'field' => 'mestnost', 'value' => $plot['mestnost']]);
                }
                if ((float)$plot['document_area'] > 0 && $plot['kvs_doc'] != $plot['document_area']) {
                    $this->updateKvs(['gid' => $plot['gid'], 'field' => 'document_area', 'value' => $plot['document_area']]);
                }
                if (!$plot['kvs_has_contr']) {
                    $this->updateKvs(['gid' => $plot['gid'], 'field' => 'has_contracts', 'value' => 't']);
                }
            }
        }
    }

    public function validateData()
    {
        $this->validContracts = $this->validateContracts($this->getInvalidContracts());
        $this->validCDates = $this->validateContractDates();
        $this->validFarmName = $this->validateClientName();
        $this->validPlots = $this->validatePlots();
        $this->validCType = $this->validateContractTypes($this->contractTypes);
        $this->validOwnersEgns = $this->validateContractOwnersEgn();
        $this->validOwnersTypes = $this->validateOwnerTypes();
        if (!$this->validContracts || !$this->validCDates || !$this->validFarmName || !$this->validPlots || !$this->validCType || !$this->validOwnersEgns) {
            $this->errorImportLog(
                print_r([
                    'contract_data' => $this->validContracts,
                    'dates' => $this->validCDates,
                    'farmer_name' => $this->validFarmName,
                    'plots_data' => $this->validPlots,
                    'contracts_types' => $this->validCType,
                    'owners_egns' => $this->validOwnersEgns,
                    'owners_types' => $this->validOwnersTypes,
                ], true)
            );

            return false;
        }

        return true;
    }

    public function setUser($user)
    {
        if (is_int((int)$user)) {
            $user_db_where = 'id = ' . $user;
        } elseif (false !== strpos($user, 'db')) {
            $user_db_where = "database = '{$user}'";
        } else {
            $user_db_where = "username = '{$user}'";
        }
        $statement = $this->mainPDO->prepare("SELECT id, username, database FROM su_users u WHERE ({$user_db_where})");
        $statement->execute();
        $users = $statement->fetchAll(PDO::FETCH_ASSOC);
        if (empty($users)) {
            $this->output->writeln('Error: No user found with ' . $user_db_where);

            return false;
        }
        if (count($users) > 1) {
            $this->output->writeln('Error: Too many user found with ' . $user_db_where);

            return false;
        }
        $this->user = $users[0];
        $this->userId = $this->user['id'];
        $this->userName = $this->user['username'];
        $this->userDb = $this->user['database'];
        $user_dns = 'pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $this->userDb . ';';
        $this->userPDO = new PDO($user_dns, DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        return $this->user;
    }

    public function tableExists($table_name, $database)
    {
        $sql = "SELECT count(*) FROM information_schema.tables WHERE table_name = '{$table_name}'  AND table_catalog = '{$database}' AND table_schema = 'public'";
        $statement = $this->userPDO->prepare($sql);
        $statement->execute();
        $result = $statement->fetchAll(PDO::FETCH_ASSOC);

        return 1 == (int)$result[0]['count'];
    }

    /**
     * @return array
     */
    public function getUsersFarmings()
    {
        $sql = $this->mainPDO->prepare("SELECT id, name from su_users_farming where user_id = {$this->userId}");
        $sql->execute();
        $user_farms = $sql->fetchAll(PDO::FETCH_ASSOC);
        if (false !== $user_farms) {
            $this->farmings = $user_farms;
        }

        return $user_farms;
    }

    /**
     * @return array|bool
     */
    public function getContractsToInsert()
    {
        $contracts_sql = $this->userPDO->prepare("SELECT DISTINCT ON (f2.c_num, f2.start_date, f2.due_date, f2.c_date, f2.owner_farming) f2.* from {$this->stubTable} f2");
        $contracts_sql->execute();
        $contracts = $contracts_sql->fetchAll(PDO::FETCH_ASSOC);
        if (false === $contracts || empty($contracts)) {
            // maybe we area loading ownership contracts, so no due_date
            $contracts_sql = $this->userPDO->prepare("SELECT DISTINCT ON (f2.c_num, f2.start_date, f2.c_date, f2.owner_farming) f2.* from {$this->stubTable} f2");
            $contracts_sql->execute();
            $contracts = $contracts_sql->fetchAll(PDO::FETCH_ASSOC);
        }
        if (false !== $contracts && !empty($contracts)) {
            $this->contractsToInsert = $contracts;

            return $this->contractsToInsert;
        }

        return;
    }

    public function truncate()
    {
        $command = $this->userPDO->prepare('
        TRUNCATE 
        su_contracts, 
        su_contracts_rents, 
        su_contracts_plots_rel, 
        su_owners, 
        su_owners_reps, 
        su_plots_owners_rel,
        su_hypothecs,
        su_hypothecs_plots_rel 
        RESTART IDENTITY CASCADE;');

        return $command->execute();
    }

    /**
     * @param string $farm_name
     *
     * @return int
     */
    public function setClientFarmId($farm_name)
    {
        $farm_name = mb_strtolower(trim($farm_name), 'UTF-8');
        $default_farm = mb_strtolower('Основно стопанство');
        foreach ($this->farmings as $farm_obj) {
            // look for the farm name and get the id
            $farm_obj_name = mb_strtolower($farm_obj['name'], 'UTF-8');
            if ($farm_name == $farm_obj_name || $farm_name == $default_farm) {
                return $farm_obj['id'];
            }
        }

        return false;
    }

    public function setContractType($contract_type)
    {
        $contract_type = mb_strtolower(trim($contract_type), 'UTF-8');
        foreach ($this->contractTypes as $contract_string => $type_id) {
            $contract_string = mb_strtolower($contract_string, 'UTF-8');
            if ($contract_type == $contract_string || false !== strpos($contract_string, $contract_type)) {
                return $type_id;
            }
        }

        return false;
    }

    public function logInvalidContracts($contract, $contractIndex = 0)
    {
        $valid = true;
        if (null == $contract['c_num'] || '' == $contract['c_num']) {
            $valid = false;
            $this->errorImportLog('WARNING invalid / empty c_num for contract on row : ' . $contractIndex);
        }
        if ('' == $contract['nm_usage_rights'] || false == $contract['nm_usage_rights']) {
            $valid = false;
            $this->errorImportLog('WARNING invalid nm_usage_rights on contract with c_num: ' . $contract['c_num'] . ' and usage_right of :' . $contract['orig_usage_rights']);
        }
        if ('' == $contract['start_date']) {
            $valid = false;
            $this->errorImportLog('WARNING empty start_date on contract with c_num: ' . $contract['c_num'] . ' and id:' . $contract['id']);
        }
        if ('' == $contract['due_date'] && 1 != $contract['nm_usage_rights']) {
            $valid = false;
            $this->errorImportLog('WARNING empty due_date on contract with c_num: ' . $contract['c_num'] . ' and id:' . $contract['id']);
        }
        if ('' == $contract['owner_farming_id'] || false == $contract['owner_farming_id']) {
            $valid = false;
            $this->errorImportLog('WARNING invalid owner_farming_id on contract with c_num: ' . $contract['c_num'] . ' and farming name : ' . $contract['owner_farming']);
        }

        return $valid;
    }

    public function setCDefaults($contract)
    {
        $contract['c_date'] = $contract['c_date'] ? $this->createDate($contract['c_date']) : null;
        $contract['start_date'] = $contract['start_date'] ? $this->createDate($contract['start_date']) : null;
        if (Config::CONTRACT_TYPE_OWN != $contract['nm_usage_rights']) {
            $contract['due_date'] = $contract['due_date'] ? $this->createDate($contract['due_date']) : $this->createDate('01.01.' . MAX_YEAR_DATE);
        } else {
            unset($contract['due_date']);
        }
        if (Config::CONTRACT_TYPE_OWN == $contract['nm_usage_rights'] && isset($contract['document_area']) && !isset($contract['contract_area'])) {
            $contract['contract_area'] = $contract['document_area'];
        }
        $contract['sv_num'] = $contract['sv_num'] ? $contract['sv_num'] : null;
        $contract['sv_date'] = $contract['sv_date'] ? $this->createDate($contract['sv_date']) : null;
        $contract['active'] = true;
        $contract['parent_id'] = 0;
        $contract['is_annex'] = false;

        $contract['renta'] = ($contract['renta'] && Config::CONTRACT_TYPE_OWN != $contract['nm_usage_rights']) ? $contract['renta'] : null;
        $contract['renta_nat'] = $contract['renta_nat'] ? $contract['renta_nat'] : null;
        $contract['renta_nat_type'] = $contract['renta_nat_type'] ? $contract['renta_nat_type'] : 0;
        $contract['renta_nat_type_id'] = $contract['renta_nat_type_id'] ? $contract['renta_nat_type_id'] : 0;

        $contract['is_sublease'] = false;
        $contract['na_num'] = $contract['na_num'] ? $contract['na_num'] : null;
        $contract['tom'] = $contract['tom'] ? $contract['tom'] : null;

        $contract['delo'] = $contract['delo'] ? $contract['delo'] : null;
        $contract['paydate'] = $contract['paydate'] ? $contract['paydate'] : null;
        $contract['comment'] = $contract['comment'] ? trim($contract['comment']) : null;
        $contract['court'] = $contract['court'] ? $contract['court'] : null;

        if (null != $contract['first_hypothec_num'] || '' != $contract['first_hypothec_num']) {
            // todo set creditor id not to be a static value/record
            $contract['first_hypothec'] = [
                'num' => $contract['first_hypothec_num'],
                'date' => !empty($contract['first_hypothec_date']) ? $contract['first_hypothec_date'] : null,
                'due_date' => !empty($contract['first_hypothec_end_date']) ? $contract['first_hypothec_end_date'] : $this->createDate('01.01.' . self::MAX_YEAR_DATE),
                'start_date' => !empty($contract['first_hypothec_date']) ? $contract['first_hypothec_date'] : null,
                'farming_id' => $contract['owner_farming_id'],
                'comment' => $contract['comment'],
                'tom' => $contract['tom'],
                'na_num' => $contract['na_num'],
                'delo' => $contract['delo'],
                'court' => $contract['court'],
                'is_active' => true,
                'deactivate_num' => null,
                'deactivate_date' => null,
                'creditor_id' => 1,
            ];
        }
        if (null != $contract['second_hypothec_num'] || '' != $contract['second_hypothec_num']) {
            $contract['second_hypothec'] = [
                'num' => $contract['second_hypothec_num'],
                'date' => !empty($contract['second_hypothec_date']) ? $contract['second_hypothec_date'] : null,
                'due_date' => !empty($contract['second_hypothec_end_date']) ? $contract['second_hypothec_end_date'] : $this->createDate('01.01.' . self::MAX_YEAR_DATE),
                'start_date' => !empty($contract['second_hypothec_date']) ? $contract['second_hypothec_date'] : null,
                'farming_id' => $contract['owner_farming_id'],
                'comment' => $contract['comment'],
                'tom' => $contract['tom'],
                'na_num' => $contract['na_num'],
                'delo' => $contract['delo'],
                'court' => $contract['court'],
                'is_active' => true,
                'deactivate_num' => null,
                'deactivate_date' => null,
                'creditor_id' => 1,
            ];
        }

        return $contract;
    }

    public function createDate($string)
    {
        if (false !== strpos($string, '/')) {
            $string = str_replace('/', '.', $string);
        }
        $piece = explode('.', $string);
        $format = 'Y-m-d';
        if (isset($piece[0], $piece[1], $piece[2])) {
            list($day, $month, $year) = $piece;
            $string = $year . '-' . $month . '-' . $day;
        }
        $return = DateTime::createFromFormat('Y-m-d', $string);
        if (false === $return) {
            return $string;
        }
        if (!$return) {
            $return = DateTime::createFromFormat('Y-m-d H:i:s', $string);
        }
        if (!$return) {
            $this->errorImportLog('WARNING!! invalid date: ' . $string);
        }
        if ($string instanceof DateTime) {
            return $return->format($format);
        }

        return $string;
    }

    public function insertContract($contract)
    {
        $insert_sql = $this->userPDO->prepare(
            'INSERT INTO su_contracts (
            c_num, c_date, nm_usage_rights, sv_num, sv_date, start_date, renta, due_date ,renta_nat, farming_id,
            active, parent_id, is_annex, renta_nat_type_id, is_sublease, comment, na_num, tom, delo 
         ) VALUES (
            :c_num, :c_date, :nm_usage_rights, :sv_num , :sv_date, :start_date, :renta, :due_date, :renta_nat, :farming_id,
            :active, :parent_id, :is_annex, :renta_nat_type_id, :is_sublease, :comment, :na_num, :tom, :delo
         )'
        );

        $insert_sql->bindValue(':c_num', $contract['c_num']);
        $insert_sql->bindValue(':c_date', $contract['c_date']);
        $insert_sql->bindValue(':nm_usage_rights', $contract['nm_usage_rights']);
        $insert_sql->bindValue(':sv_num', $contract['sv_num']);
        $insert_sql->bindValue(':sv_date', $contract['sv_date']);
        $insert_sql->bindValue(':start_date', $contract['start_date']);
        $insert_sql->bindValue(':renta', $contract['renta']);
        $insert_sql->bindValue(':due_date', $contract['due_date']);
        $insert_sql->bindValue(':renta_nat', $contract['renta_nat']);
        $insert_sql->bindValue(':farming_id', $contract['owner_farming_id']);
        $insert_sql->bindValue(':active', true, PDO::PARAM_BOOL);
        $insert_sql->bindValue(':parent_id', $contract['parent_id']);
        $insert_sql->bindValue(':is_annex', false, PDO::PARAM_BOOL);
        $insert_sql->bindValue(':renta_nat_type_id', $contract['renta_nat_type_id']);
        $insert_sql->bindValue(':is_sublease', false, PDO::PARAM_BOOL);
        $insert_sql->bindValue(':comment', $contract['comment']);
        $insert_sql->bindValue(':na_num', $contract['na_num']);
        $insert_sql->bindValue(':tom', $contract['tom']);
        $insert_sql->bindValue(':delo', $contract['delo']);

        return $this->executeAndLog($this->userPDO, 'su_contracts', $insert_sql, 'ERR failed to insert contract with c_num: ' . $contract['c_num'] . ' and start_date:' . ($contract['start_date'] ? $this->createDate($contract['start_date']) : $contract['start_date']));
    }

    public function selectContract($contract)
    {
        $sql = 'SELECT * FROM su_contracts WHERE c_num = :c_num 
        AND c_date = :c_date 
        AND nm_usage_rights = :nm_usage_rights 
        AND start_date = :start_date 
        AND farming_id = :farming_id';

        if (Config::CONTRACT_TYPE_OWN !== $contract['nm_usage_rights']) {
            $sql .= 'AND due_date = :due_date';
        }

        $select_sql = $this->userPDO->prepare($sql);
        $select_sql->bindValue(':c_num', $contract['c_num']);
        $select_sql->bindValue(':c_date', $contract['c_date']);
        $select_sql->bindValue(':nm_usage_rights', $contract['nm_usage_rights']);
        $select_sql->bindValue(':start_date', $contract['start_date']);
        $select_sql->bindValue(':farming_id', $contract['owner_farming_id']);

        if (Config::CONTRACT_TYPE_OWN !== $contract['nm_usage_rights']) {
            $select_sql->bindValue(':due_date', $contract['due_date']);
        }

        $select_sql->execute();
        $contract_exist = $select_sql->fetchAll(PDO::FETCH_ASSOC);
        if (1 === count($contract_exist)) {
            $this->errorImportLog('INFO: contract found already with id: ' . $contract_exist[0]['id'] . ' on table: su_contracts');

            return $contract_exist[0];
        }

        return;
    }

    public function executeAndLog($connection, $table, $sql, $errorMsg)
    {
        if (!$sql->execute()) {
            $error = $sql->errorInfo();
            $this->errorImportLog(print_r($errorMsg, true) . ' , error : ' . $error[2]);

            return false;
        }
        // get last inserted id
        return $connection->lastInsertId($table . '_id_seq');
    }

    public function insertContractRentaNat($contract)
    {
        $sql = 'INSERT INTO su_contracts_rents ( contract_id, renta_id, renta_value ) VALUES ( :c_id, :renta_id, :renta_value )';
        $insert_sql = $this->userPDO->prepare($sql);
        $insert_sql->bindValue(':c_id', $contract['id']);
        $insert_sql->bindValue(':renta_id', $contract['renta_nat_type_id']);
        $insert_sql->bindValue(':renta_value', $contract['renta_nat']);

        return $this->executeAndLog($this->userPDO, 'su_contracts_rents', $insert_sql, 'ERR failed to insert renta_nat in contract with c_num: ' . $contract['c_num']);
    }

    // query kvs table for real data about plots and possible overwrite using the imported data if kvsUpdate flag is set to true
    public function selectContractsPlots($contract)
    {
        if (Config::CONTRACT_TYPE_OWN != $contract['nm_usage_rights']) {
            $plots = $this->userPDO->prepare(
                "SELECT f2.*, kvs.gid, kvs.document_area as kvs_doc, kvs.category as kvs_cat ,kvs.mestnost as kvs_mest , kvs.has_contracts as kvs_has_contr from {$this->stubTable} f2 LEFT JOIN layer_kvs kvs on (f2.katident = kvs.kad_ident) where 
                f2.c_num='{$contract['c_num']}'
            and f2.start_date = '{$contract['start_date']}'
            and f2.due_date = '{$contract['due_date']}'
            and f2.c_date = '{$contract['c_date']}'
            and f2.owner_farming = '{$contract['owner_farming']}'"
            );
        } else {
            $plots = $this->userPDO->prepare(
                "SELECT f2.*, kvs.gid, kvs.document_area as kvs_doc, kvs.category as kvs_cat ,kvs.mestnost as kvs_mest , kvs.has_contracts as kvs_has_contr from {$this->stubTable} f2 LEFT JOIN layer_kvs kvs on (f2.katident = kvs.kad_ident) where 
                f2.c_num='{$contract['c_num']}'
            and f2.start_date = '{$contract['start_date']}'
            and f2.c_date = '{$contract['c_date']}'
            and f2.owner_farming = '{$contract['owner_farming']}'"
            );
        }
        $plots->execute();

        return $plots->fetchAll(PDO::FETCH_ASSOC);
    }

    public function selectSumContractsArea($plot)
    {
        if (Config::CONTRACT_TYPE_OWN != $plot['nm_usage_rights']) {
            $plot['area_for_rent'] = $plot['contract_area'];
            $katident = $plot['katident'];
            $c_num = $plot['c_num'];
            $select_sql = "SELECT sum(CAST(contract_area AS FLOAT)) as total_contract_area from {$this->stubTable} 
                WHERE katident = '{$katident}' and c_num = '{$c_num}' and start_date = '{$plot['start_date']}' and c_date = '{$plot['c_date']}'";
            if ($plot['due_date']) {
                $select_sql .= " and due_date = '{$plot['due_date']}'";
            } else {
                $select_sql .= ' and due_date is null';
            }

            $sql_a = $this->userPDO->prepare($select_sql);
            $sql_a->execute();
            $result = $sql_a->fetchAll(PDO::FETCH_ASSOC);
            $plot['total_contract_area'] = $result[0]['total_contract_area'];
        }

        if (null != $plot['percent']) {
            $plot['contract_area'] = $plot['contract_area'] * $plot['percent'] / 100;
        }

        return $plot;
    }

    public function selectContractPlotRel(&$plot, $contractId)
    {
        $id = null;
        $contract_plot_rel = $this->userPDO->prepare("select * from su_contracts_plots_rel where contract_id = {$contractId} AND plot_id = {$plot['gid']}");
        $contract_plot_rel->execute();
        $result = $contract_plot_rel->fetchAll(PDO::FETCH_ASSOC);
        if (empty($result)) {
            return;
        }
        $id = $result[0]['id'];
        $this->errorImportLog('INFO - relation found between plot with gid : ' . $plot['gid'] . ', contract (id): ' . $contractId . ' and su_contracts_plots_rel with id ' . $id);

        return $id;
    }

    public function insertOwnershipContractsPlotsRel($contractId, $plot)
    {
        $insert_sql = $this->userPDO->prepare('INSERT INTO su_contracts_plots_rel 
            ( contract_id, plot_id, contract_area, annex_action, area_for_rent, fraction, percent, contract_end_date, price_per_acre) VALUES 
            (:contract_id, :gid, :contract_area, :annex_action, :area_for_rent, :fraction, :percent, :contract_end_date, :price_per_acre )
        ');
        $insert_sql->bindValue(':contract_id', $contractId);
        $insert_sql->bindValue(':gid', $plot['gid']);
        $insert_sql->bindValue(':contract_area', (float)$plot['contract_area']);
        $insert_sql->bindValue(':annex_action', 'added');
        $insert_sql->bindValue(':fraction', 1);
        $insert_sql->bindValue(':percent', 100);
        $insert_sql->bindValue(':area_for_rent', null);
        $insert_sql->bindValue(':contract_end_date', null);
        $insert_sql->bindValue(':price_per_acre', $plot['price_per_acre']);

        return $this->executeAndLog($this->userPDO, 'su_contracts_plots_rel', $insert_sql, 'ERR failed to insert contract-plot relation  with contract_id: ' . $contractId . ' and plot gid: ' . $plot['gid']);
    }

    public function insertRentContractsPlotsRel($contractId, $plot)
    {
        $insert_sql = $this->userPDO->prepare('INSERT INTO su_contracts_plots_rel
           ( contract_id, plot_id, contract_area, annex_action, area_for_rent, fraction, percent, contract_end_date ) VALUES 
           ( :contract_id, :gid, :contract_area, :annex_action, :area_for_rent, :fraction, :percent, :contract_end_date )
        ');
        $insert_sql->bindValue(':contract_id', $contractId);
        $insert_sql->bindValue(':gid', $plot['gid']);
        $insert_sql->bindValue(':contract_area', (float)$plot['total_contract_area']);
        $insert_sql->bindValue(':annex_action', 'added');
        $insert_sql->bindValue(':fraction', null);
        $insert_sql->bindValue(':percent', null);
        $insert_sql->bindValue(':area_for_rent', $plot['total_contract_area']);
        $insert_sql->bindValue(':contract_end_date', $plot['due_date']);

        return $this->executeAndLog($this->userPDO, 'su_contracts_plots_rel', $insert_sql, 'ERR failed to insert contract-plot relation  with contract_id: ' . $contractId . ' and plot gid: ' . $plot['gid']);
    }

    public function updateContractPlotRel($plot, $contractsPlotsRelId)
    {
        $update_rel = $this->userPDO->prepare("
            update su_contracts_plots_rel SET 
            contract_area = :contract_area, 
            area_for_rent = :area_for_rent, 
            fraction = :fraction, 
            percent = :percent, 
            contract_end_date = :contract_end_date 
            where id = {$contractsPlotsRelId};
            ");
        if (Config::CONTRACT_TYPE_OWN === $plot['nm_usage_rights']) {
            $update_rel->bindValue(':contract_area', (float)$plot['contract_area']);
            $update_rel->bindValue(':area_for_rent', null);
            $update_rel->bindValue(':fraction', 1);
            $update_rel->bindValue(':percent', 100);
            $update_rel->bindValue(':contract_end_date', null);
        } else {
            $update_rel->bindValue(':contract_area', (float)$plot['total_contract_area']);
            $update_rel->bindValue(':area_for_rent', $plot['total_contract_area']);
            $update_rel->bindValue(':fraction', null);
            $update_rel->bindValue(':percent', null);
            $update_rel->bindValue(':contract_end_date', $plot['due_date']);
        }
        $update_rel->execute();
    }

    public function getOrCreateOwner($plot, $bypassEGN)
    {
        $owner_name = mb_strtolower(trim($plot['owner_name']), 'UTF-8');
        $owner_surname = mb_strtolower(trim($plot['owner_surname']), 'UTF-8');
        $owner_family = mb_strtolower(trim($plot['familly']), 'UTF-8');
        if (empty($owner_name) && empty($owner_surname) && empty($owner_family)) {
            return;
        }
        if ('ЮЛ' === mb_strtoupper($plot['owner_type'], 'UTF-8')) {
            $egn_eik_column = 'eik';
            $owner_type = 0;
        } else {
            $egn_eik_column = 'egn';
            $owner_type = 1;
        }

        if (!empty($plot['egn_eik']) && !$bypassEGN) {
            // search by EGN on the plot
            $owner_exists = $this->userPDO->prepare(" select * from su_owners where {$egn_eik_column} = '{$plot['egn_eik']}' ");
            $nameSearched = false;
        } else {
            // search by the name
            $nameSearched = true;
            if (1 == $owner_type) {
                $owner_exists = $this->userPDO->prepare("select * from su_owners where trim(lower(name)) = '{$owner_name}' AND trim(lower(surname)) = '{$owner_surname}' AND trim(lower(lastname)) = '{$owner_family}' ");
            } else {
                $owner_exists = $this->userPDO->prepare("select * from su_owners where trim(lower(company_name)) = '{$owner_name}'");
            }
        }
        $owner_exists->execute();
        $result = $owner_exists->fetchAll(PDO::FETCH_ASSOC);
        if (empty($result)) {
            $owner_id = $this->insertOwner($owner_type, $plot);
        } else {
            // Show in log that searching is by name and it is good idea to check it manually because name is not always unique
            if ($nameSearched) {
                echo print_r('************* WARNING: The owner below was maybe found by name. Please check for eventually duplication  ***************', true) . "\r\n";
            }
            $owner_id = $result[0]['id'];
        }

        return $owner_id;
    }

    public function insertOwner($owner_type, $plot)
    {
        $plot['owner_address'] = !empty($plot['address']) ? $plot['address'] . ', ' . $plot['owner_address'] : null;
        $phone = !empty($plot['phone']) ? $plot['phone'] : null;
        if ($plot['egn_eik_farming']) {
            $plot['egn_eik'] = $plot['egn_eik_farming'];
        }
        $owner_sql = 'INSERT INTO su_owners ( name, surname, lastname, egn, lk_nomer, lk_izdavane, company_name, eik, phone, fax, mobile, email, address, owner_type, mol, company_address, is_dead, iban )';
        // owner_type == 1 : individual , 0 == company
        if (1 == $owner_type) {
            $owner_sql .= "
            VALUES (
            '{$plot['owner_name']}', '{$plot['owner_surname']}', '{$plot['familly']}', '{$plot['egn_eik']}', NULL, NULL, NULL, NULL, '{$phone}', NULL, NULL, NULL, '{$plot['owner_address']}', {$owner_type}, NULL, NULL, false, NULL )";
        } else {
            $owner_sql .= "
            VALUES (
            NULL, NULL, NULL, NULL, NULL, NULL, '{$plot['owner_name']}', '{$plot['egn_eik']}', '{$phone}', NULL, NULL, NULL, NULL, {$owner_type}, NULL, '{$plot['owner_address']}', false, NULL )";
        }
        $create_owner_sql = $this->userPDO->prepare($owner_sql);

        return $this->executeAndLog($this->userPDO, 'su_owners', $create_owner_sql, 'ERR failed owner creation with egn/eik: ' . $plot['egn_eik']);
    }

    public function getOrCreateRep(&$plot, $ownerId)
    {
        $rep_id = null;
        $rep = null;
        $hasRepresentative = false;
        if (!empty($plot['rep_name'])
            || !empty($plot['rep_surname'])
            || !empty($plot['rep_familly'])
            || !empty($plot['rep_egn'])) {
            $hasRepresentative = true;
            $plot['rep_name'] = !empty($plot['rep_name']) ? trim($plot['rep_name']) : null;
            $plot['rep_surname'] = !empty($plot['rep_surname']) ? trim($plot['rep_surname']) : null;
            $plot['rep_familly'] = !empty($plot['rep_familly']) ? trim($plot['rep_familly']) : null;
            $plot['rep_egn'] = !empty($plot['rep_egn']) ? trim($plot['rep_egn']) : '';
        } else {
            $plot['rep_name'] = !empty($plot['owner_name']) ? trim($plot['owner_name']) : null;
            $plot['rep_surname'] = !empty($plot['owner_surname']) ? trim($plot['owner_surname']) : null;
            $plot['rep_familly'] = !empty($plot['familly']) ? trim($plot['familly']) : null;
            $plot['rep_egn'] = !empty($plot['egn_eik']) ? trim($plot['egn_eik']) : '';
        }

        if (!empty($plot['rep_egn'])) {
            $nameSearched = false;
            // search by EGN
            $rep_exists = $this->userPDO->prepare(" select * from su_owners_reps where rep_egn = '{$plot['rep_egn']}' ");
        } else {
            // search by the name
            $nameSearched = true;
            $rep_exists = $this->userPDO->prepare(" select * from su_owners_reps where rep_name = '{$plot['rep_name']}' AND (rep_surname) = '{$plot['rep_surname']}' AND (rep_lastname) = '{$plot['rep_familly']}' ");
        }

        $rep_exists->execute();
        $rep = $rep_exists->fetchAll(PDO::FETCH_ASSOC);

        if (empty($rep)) {
            // If there is representative for this owner, but it doesn't exist in su_owners_reps, we should make a record in it with owner_id = null
            $rep_id = $this->createRep($plot, $hasRepresentative ? null : $ownerId);
        } else {
            // Show in log that searching is by name and it is good idea to check it manually because name is not always unique
            if (!empty($nameSearched)) {
                echo print_r('************* WARNING: The representative below was maybe found by name. Please check for eventually duplication  ***************', true) . "\r\n";
            }
            $rep_id = $rep[0]['id'];
        }

        return $rep_id;
    }

    public function createRep(&$plot, $owner_id)
    {
        $ownerId = $owner_id ? $owner_id : 'NULL';
        $rep_sql = 'INSERT INTO su_owners_reps (rep_name, rep_surname, rep_lastname, rep_egn, rep_lk, rep_lk_izdavane, rep_address, owner_id, rent_place) ';
        $rep_sql .= " VALUES ('{$plot['rep_name']}', '{$plot['rep_surname']}', '{$plot['rep_familly']}', {$plot['rep_egn']}, NULL, NULL, NULL, {$ownerId}, NULL )";

        $create_rep_sql = $this->userPDO->prepare($rep_sql);

        return $this->executeAndLog($this->userPDO, 'su_owners_reps', $create_rep_sql, 'ERR failed adding representative on owner with egn/eik: ' . $plot['egn_eik']);
    }

    public function selectPlotOwnersRelation($params)
    {
        $select_sql = $this->userPDO->prepare('SELECT * FROM  su_plots_owners_rel  
          WHERE pc_rel_id = :pc_rel_id 
          AND owner_id = :owner_id 
          AND percent = :percent
          AND numerator = :numerator 
          AND denominator = :denominator ');
        $select_sql->bindValue(':pc_rel_id', $params['pc_rel_id']);
        $select_sql->bindValue(':owner_id', $params['owner_id']);
        $select_sql->bindValue(':percent', $params['owner_percent']);
        $select_sql->bindValue(':numerator', $params['numerator']);
        $select_sql->bindValue(':denominator', $params['denominator']);
        $select_sql->execute();
        $result = $select_sql->fetchAll(PDO::FETCH_ASSOC);

        return (count($result) >= 1) ? $result[0]['id'] : null;
    }

    public function insertPlotsOwnersRel($params)
    {
        $insert_sql = $this->userPDO->prepare(
            'INSERT INTO su_plots_owners_rel (
        pc_rel_id, owner_id, percent, owner_document_id, rep_id, proxy_num, proxy_date, path, is_heritor, numerator, denominator 
        ) VALUES (
        :pc_rel_id, :owner_id, :percent, NULL, :rep_id, NULL, NULL, NULL, FALSE, :numerator, :denominator)'
        );
        $insert_sql->bindValue(':pc_rel_id', $params['pc_rel_id']);
        $insert_sql->bindValue(':owner_id', $params['owner_id']);
        $insert_sql->bindValue(':percent', $params['owner_percent']);
        $insert_sql->bindValue(':rep_id', $params['rep_id']);
        $insert_sql->bindValue(':numerator', $params['numerator']);
        $insert_sql->bindValue(':denominator', $params['denominator']);
        if (!$insert_sql->execute()) {
            $error = $insert_sql->errorInfo();
            $this->errorImportLog($error . ' , error : ' . $error[2]);

            return false;
        }
        // get last inserted id
        return $this->userPDO->lastInsertId('su_plots_owners_rel_id_seq');
    }

    public function selectHypothec($params)
    {
        $select_hypotech = $this->userPDO->prepare('
            SELECT id from su_hypothecs  
            where num = :num 
            AND start_date = :start_date 
            AND date = :date 
            AND due_date = :due_date 
            AND farming_id = :farming_id');
        $select_hypotech->bindValue(':num', $params['num']);
        $select_hypotech->bindValue(':start_date', $params['start_date']);
        $select_hypotech->bindValue(':date', $params['date']);
        $select_hypotech->bindValue(':due_date', $params['due_date']);
        $select_hypotech->bindValue(':farming_id', $params['farming_id']);
        $select_hypotech->execute();

        $existing_hypotech = $select_hypotech->fetchAll(PDO::FETCH_ASSOC);
        if (empty($existing_hypotech)) {
            return;
        }
        $this->errorImportLog('INFO: hypothec already found with id: ' . $existing_hypotech[0]['id'] . ' on table: su_hypothecs for hypothec number: ' . $params['num']);

        return $existing_hypotech[0]['id'];
    }

    public function selectHypothecPlotRel($plotId, $hypothecId, $hypothecNum)
    {
        $selectHypothecRel = $this->userPDO->prepare(
            'SELECT * from su_hypothecs_plots_rel where 
              hypothec_id = :hypothec_id
              AND plot_id = :plot_id'
        );

        $selectHypothecRel->bindValue(':hypothec_id', $hypothecId);
        $selectHypothecRel->bindValue(':plot_id', $plotId);

        $selectHypothecRel->execute();

        $existingHypothecRel = $selectHypothecRel->fetchAll(PDO::FETCH_ASSOC);
        if (empty($existingHypothecRel)) {
            return;
        }
        $this->errorImportLog('INFO: hypothec_plot_rel already found for : ' . $hypothecNum . ' and plot with id' . $plotId);

        return $existingHypothecRel[0]['id'];
    }

    public function insertHypothec($params)
    {
        $hypotech_sql = $this->userPDO->prepare('INSERT INTO su_hypothecs (num, date, due_date, comment, creditor_id, start_date, farming_id, tom, na_num, delo, court, is_active,deactivate_num,deactivate_date) 
    VALUES (:num, :date, :due_date, :comment, :creditor_id, :start_date, :farming_id, :tom, :na_num, :delo, :court, :is_active, :deactivate_num, :deactivate_date)');
        $hypotech_sql->bindValue(':num', $params['num']);
        $hypotech_sql->bindValue(':date', $params['date']);
        $hypotech_sql->bindValue(':due_date', $params['due_date']);
        $hypotech_sql->bindValue(':start_date', $params['start_date']);
        $hypotech_sql->bindValue(':farming_id', $params['farming_id']);
        $hypotech_sql->bindValue(':comment', $params['comment']);
        $hypotech_sql->bindValue(':tom', $params['tom']);
        $hypotech_sql->bindValue(':na_num', $params['na_num']);
        $hypotech_sql->bindValue(':delo', $params['delo']);
        $hypotech_sql->bindValue(':court', $params['court']);
        $hypotech_sql->bindValue(':is_active', $params['is_active']);
        $hypotech_sql->bindValue(':deactivate_num', $params['deactivate_num']);
        $hypotech_sql->bindValue(':deactivate_date', $params['deactivate_date']);
        $hypotech_sql->bindValue(':creditor_id', $params['creditor_id']);

        return $this->executeAndLog($this->userPDO, 'su_hypothecs', $hypotech_sql, 'ERR failed insert in table su_hypothecs hypothecs with id: ' . $params['num']);
    }

    public function insertHypothecPlotRel($params)
    {
        $hypotech_sql = $this->userPDO->prepare("INSERT INTO su_hypothecs_plots_rel ( hypothec_id, plot_id, hypothec_area ) 
            VALUES ({$params['hypothec_id']}, {$params['plot_id']}, {$params['hypothec_area']} )");

        return $this->executeAndLog($this->userPDO, 'su_hypothecs_plots_rel', $hypotech_sql, 'ERR failed insert in table su_hypothecs_plots_rel pc_rel_id: ' . print_r($params, true));
    }

    public function updateKvs($params)
    {
        $kvs_sql = $this->userPDO->prepare("UPDATE layer_kvs SET {$params['field']} = :value where gid = {$params['gid']};");
        $kvs_sql->bindValue(':value', $params['value']);
        $kvs_sql->execute();
        $this->errorImportLog("INFO: updated KVS plot with gid: {$params['gid']} on field: {$params['field']} with value: {$params['value']}");
    }

    public function setOwnerPercent($plot)
    {
        if (!$plot['owner_percent']) {
            if (0 == (int)ceil($plot['total_contract_area']) || 0 == (int)ceil($plot['contract_area'])) {
                $plot['owner_percent'] = 0;
            } else {
                $plot['owner_percent'] = round(($plot['contract_area'] / $plot['total_contract_area']) * 100, 1);
            }
        }

        if (0 == $plot['owner_percent']) {
            $plot['denominator'] = 0;
            $plot['numerator'] = 0;
        } else {
            $fraction = $this->dec2fraction((int)$plot['owner_percent']);
            if (is_array($fraction)) {
                $plot['denominator'] = $fraction['den'];
                $plot['numerator'] = $fraction['num'];
            }
        }

        return $plot;
    }

    public function dec2fraction($decimal)
    {
        $decimal = (string)$decimal;
        $num = '';
        $den = 1;
        $dec = false;

        // find least reduced fractional form of number
        for ($i = 0, $ix = strlen($decimal); $i < $ix; $i++) {
            // build the denominator as we 'shift' the decimal to the right
            if ($dec) {
                $den *= 10;
            }

            // find the decimal place/ build the numerator
            if ('.' === $decimal[$i]) {
                $dec = true;
            } else {
                $num .= $decimal[$i];
            }
        }
        $num = (int)$num;

        // whole number, just return it
        if (1 == $den) {
            return $num;
        }

        $num2 = $num;
        $den2 = $den;
        $rem = 1;
        // Euclid's Algorithm (to find the gcd)
        while ($num2 % $den2) {
            $rem = $num2 % $den2;
            $num2 = $den2;
            $den2 = $rem;
        }
        if ($den2 != $den) {
            $rem = $den2;
        }
        // now $rem holds the gcd of the numerator and denominator of our fraction
        return ['num' => $num / $rem, 'den' => $den / $rem];
    }

    public function getInvalidContracts()
    {
        $command = $this->userPDO->prepare(
            "SELECT c_num, nm_usage_rights, start_date, katident, c_date, owner_farming, contract_area, document_area from {$this->stubTable} as st WHERE 
         c_num is null OR nm_usage_rights is null OR start_date is null OR katident is null OR 
         c_date is null OR owner_farming is null OR contract_area is null;"
        );
        $command->execute();

        return $command->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * @param array $invalidData
     *
     * @return bool
     */
    public function validateContracts($invalidData)
    {
        if (!empty($invalidData)) {
            $this->errorImportLog('Records with missing data for property number, contract type, start date, date of conclusion, farm, area under contract or area per document:');
            foreach ($invalidData as $contract) {
                foreach ($contract as $field => $value) {
                    if (null == $value) {
                        $this->errorImportLog('WARNING plot with кадидент: ' . $contract['katident'] . ' with contract number ' . $contract['c_num'] . ' has this field null ' . $field);
                    }
                }
            }

            return false;
        }
        $this->errorImportLog('contracts data: ok');

        return true;
    }

    /**
     * @return bool
     */
    public function validateContractDates()
    {
        $command = $this->userPDO->prepare("SELECT katident from {$this->stubTable} as st WHERE due_date < start_date;");
        $command->execute();
        $incorrect_dates = $command->fetchAll(PDO::FETCH_ASSOC);
        // check where end / due_date start before start_date
        if (!empty($incorrect_dates)) {
            // $this->error_import_log('Записи с крайна дата преди началната:');
            $this->errorImportLog('End date entries before start:');
            foreach ($incorrect_dates as $value) {
                $this->errorImportLog('WARNING Invalid due_date/start_date for plot: ' . $value['katident']);
            }

            return false;
        }
        $this->errorImportLog('contracts dates: ok');

        return true;
    }

    /**
     * @return bool
     */
    public function validateClientName()
    {
        // stopanstwa, sashtestvuvashti v akaunta;
        $command = $this->mainPDO->prepare('SELECT DISTINCT(name) FROM su_users_farming WHERE user_id = :user_id;');
        $command->bindValue('user_id', $this->userId);
        $command->execute();
        $user_farms = $command->fetchAll(PDO::FETCH_ASSOC);
        $user_farms = $this->arrayForQueryTransformFarming($user_farms);
        $select_sql = $this->userPDO->prepare("SELECT * from {$this->stubTable} as st WHERE owner_farming NOT IN ({$user_farms}) OR owner_farming is NULL;");
        $select_sql->execute();
        $missing_farms = $select_sql->fetchAll(PDO::FETCH_ASSOC);
        $errors = [];
        if (!empty($missing_farms)) {
            $this->errorImportLog('Holdings of the account: ' . print_r($user_farms, true));
            foreach ($missing_farms as $value) {
                $errors[] = 'WARNING Липсващо стопанство: ' . $value['owner_farming'] . ' към имот: ' . $value['katident'];
            }
            $errors = array_unique($errors);
            asort($errors);
            foreach ($errors as $error) {
                $this->errorImportLog($error);
            }

            return false;
        }
        $this->errorImportLog('client farm name: ok');

        return true;
    }

    /**
     * @return bool
     */
    public function validatePlots()
    {
        $command = $this->userPDO->prepare(
            "SELECT DISTINCT(katident) from {$this->stubTable} as st WHERE katident NOT IN ( SELECT kad_ident from layer_kvs WHERE kad_ident IS NOT NULL ) OR katident is NULL;"
        );
        $command->execute();
        $missing_plots = $command->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($missing_plots)) {
            asort($missing_plots);
            $this->missingPlots = $missing_plots;
            foreach ($missing_plots as $value) {
                if (empty($value['katident'])) {
                    $this->errorImportLog('WARNING Null KadIdent for contract : ' . $value['c_num']);
                } else {
                    $this->errorImportLog('WARNING Missing plot in a KBC layer with katident : ' . $value['katident']);
                }
            }

            return false;
        }
        $this->errorImportLog('plots: ok');

        return true;
    }

    /**
     * @param array $contract_types
     *
     * @return bool
     */
    public function validateContractTypes($contract_types)
    {
        // типове договори
        $contract_types = array_keys($contract_types);
        $types_sql = $this->userPDO->prepare("SELECT * from {$this->stubTable} as st WHERE nm_usage_rights NOT IN ({$contract_types});");
        $types_sql->execute();
        $invalid_types = $types_sql->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($invalid_types)) {
            // $this->error_import_log("Записи с некоректни типове на договори:");
            $this->errorImportLog('Records with incorrect types of contracts:');
            foreach ($invalid_types as $value) {
                $this->errorImportLog('WARNING Некоректент тип на договор: ' . $value['c_num'] . ' с тип: ' . $value['nm_usage_rights']);
            }

            return false;
        }
        $this->errorImportLog('contracts types ok.');

        return true;
    }

    public function validateOwnerTypes()
    {
        $select_sql = $this->userPDO->prepare("SELECT * from {$this->stubTable} as st;");
        $select_sql->execute();
        $import_data = $select_sql->fetchAll(PDO::FETCH_ASSOC);
        $errors = [];
        $this->errorImportLog(PHP_EOL . 'Records with incorrect owner types');
        foreach ($import_data as $row) {
            $owner_type = mb_strtoupper($row['owner_type'], 'UTF-8');
            $egn_eik = $row['egn_eik'];
            $length = strlen($egn_eik);
            if ('ЮЛ' === $owner_type && 9 !== $length) {
                $errors[] = $this->errorImportLog('WARNING wrong EIK length: ' . $length . ' for EIK :' . $egn_eik . ' on plot: ' . $row['katident'] . ' or wrong owner_type: ' . $owner_type);
            } elseif ('ФЛ' === $owner_type && 10 !== $length) {
                $errors[] = $this->errorImportLog('WARNING wrong EGN length: ' . $length . ' for EGN :' . $egn_eik . ' on plot: ' . $row['katident'] . ' or wrong owner_type: ' . $owner_type);
            }
        }
        if (empty($errors)) {
            $this->errorImportLog('contracts types ok.' . PHP_EOL);

            return true;
        }

        return false;
    }

    /**
     * @return bool
     */
    public function validateContractOwnersEgn()
    {
        $select_sql = $this->userPDO->prepare("select distinct owner_name, owner_surname, familly, egn_eik ,owner_type from {$this->stubTable};");
        $select_sql->execute();
        $owners = $select_sql->fetchAll(PDO::FETCH_ASSOC);
        $valid = true;
        $temp_egn_eik = [];
        $errors = [];
        if (!empty($owners)) {
            foreach ($owners as $row) {
                if (!in_array($row['egn_eik'], $temp_egn_eik, true)) {
                    $temp_egn_eik[] = $row['egn_eik'];
                } else {
                    $valid = false;
                    if (empty($row['egn_eik'])) {
                        $errors[] = 'WARNING EMPTY EGN/EIK number for Owner: ' . $row['owner_name'] . ' ' . $row['owner_surname'] . ' ' . $row['familly'];
                    } else {
                        $errors[] = 'WARNING Duplicate / not unique EGN number: ' . $row['egn_eik'] . ' for Owner: ' . $row['owner_name'] . ' ' . $row['owner_surname'] . ' ' . $row['familly'];
                    }
                }
                $strlen = strlen($row['egn_eik']);
                if (10 != $strlen && 9 != $strlen) {
                    $valid = false;
                    $errors[] = 'WARNING invalid length of egn_eik: ' . strlen($row['egn_eik']) . ' (value: ' . $row['egn_eik'] . ') for owner: ' . $row['owner_name'] . ' ' . $row['owner_surname'] . ' ' . $row['familly'];
                }
            }
            if ($valid) {
                $this->errorImportLog('owners data ok.');
            } else {
                $errors = array_unique($errors);
                asort($errors);
                foreach ($errors as $error) {
                    $this->errorImportLog($error);
                }
            }

            return $valid;
        }
        $this->errorImportLog('no owners present!');

        return false;
    }

    public function getUser()
    {
        return $this->user;
    }

    public function getUserPDO()
    {
        return $this->userPDO;
    }

    public function getMainPDO()
    {
        return $this->mainPDO;
    }

    public function setTable($table)
    {
        $this->stubTable = $table;
    }

    public function getLogPath()
    {
        return $this->logPath;
    }

    public function resetLog()
    {
        return file_put_contents($this->logPath, '');
    }

    public function readLog()
    {
        return file_get_contents($this->logPath);
    }

    public function getInsertedContracts()
    {
        return $this->contractsToInsert;
    }

    protected function configure()
    {
        $this->setName('tf:import_contracts_command')
            ->setDescription('Import contracts for user given a specific table storing contract data to be imported')
            ->addArgument('user_db', InputArgument::OPTIONAL, 'The user db name')
            ->addArgument('table_name', InputArgument::OPTIONAL, 'The table name where the source data resides')
            ->addOption(
                'truncate',
                't',
                InputOption::VALUE_OPTIONAL,
                'This options reset the following tables for the specified user before importing:
                {su_contracts,su_contracts_rents,su_contracts_plots_rel,su_owners,su_owners_reps,su_plots_owners_rel,su_hypothecs,su_hypothecs_plots_rel}.
                 Default false',
                false
            )->addOption(
                'force',
                'f',
                InputOption::VALUE_OPTIONAL,
                'Force cause the script importing the data regardless of the validation result. Default false',
                false
            )->addOption(
                'updateKvs',
                'k',
                InputOption::VALUE_OPTIONAL,
                'Update contract area, document area, category based on the data in the import table',
                false
            );
    }

    /**
     * @return null|int
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_db = $input->getArgument('user_db');
        $table = $input->getArgument('table_name');
        $helper = $this->getHelper('question');
        $this->input = $input;
        $this->output = $output;
        if (empty($user_db)) {
            $question = new Question('Please Specify an user, either by id or by username or by db_name:' . PHP_EOL);
            $answer = $helper->ask($input, $output, $question);
            if (!$answer) {
                $this->errorImportLog('No user specified, exiting');

                return self::ERROR_USER_NOT_FOUND;
            }
            $user_db = $answer;
        }

        if (empty($table)) {
            $question = new Question('Please Specify a table to import: ' . PHP_EOL);
            $answer = $helper->ask($input, $output, $question);
            if (!$answer) {
                $this->errorImportLog('No table to import specified, exiting');

                return self::ERROR_TABLE_NOT_FOUND;
            }
            $table = $answer;
        }
        $this->setUser($user_db);
        if (!$this->user) {
            $this->errorImportLog('user not found, exiting.');

            return self::ERROR_USER_NOT_FOUND;
        }
        $this->errorImportLog('Info: found user ' . $this->userName);
        $table_exists = $this->tableExists($table, $this->userDb);
        if (!$table_exists) {
            $this->errorImportLog("table '{$table}' does not exist on db '{$this->userDb}' ");

            return self::ERROR_TABLE_NOT_FOUND;
        }
        $this->stubTable = $table;
        $this->errorImportLog('Info: found table to import: ' . $table);

        $base_dir = dirname(dirname(dirname(__DIR__)));

        require_once $base_dir . '/protected/Common/Config.php';

        $this->errorImportLog($base_dir);

        $this->countIndexes();

        $this->forceRun = (bool)$input->getOption('force');
        $this->truncateTables = (bool)$input->getOption('truncate');
        $this->updateKvs = (bool)$input->getOption('updateKvs');

        $valid = $this->validateData();

        if (!$this->forceRun && !$valid) {
            $this->errorImportLog('data in import table is not valid');

            return self::ERROR_INVALID_DATA;
        }
        $this->errorImportLog('Import Contract Process started on user_db: ' . $this->userDb . ', table: ' . $this->stubTable . ', time: ' . date('Y-m-d h:i:s'));

        $this->getUsersFarmings();
        if (empty($this->farmings)) {
            $this->errorImportLog('User has no farming! Exiting command.');

            return self::ERROR_MISSING_USER_FARM;
        }

        $this->getContractsToInsert();
        if (null === $this->contractsToInsert) {
            $this->errorImportLog('No contracts found to insert! Exiting command.');

            return self::ERROR_NO_CONTRACTS_TO_LOAD;
        }

        if ($this->truncateTables) {
            $this->truncate();
        }

        $this->importContracts($this->contractsToInsert);
        $this->errorImportLog('Import Contract Process ended for user_db: ' . $this->userDb . ', table: ' . $this->stubTable . ', time: ' . date('Y-m-d h:i:s'));

        return self::STATUS_SUCCESS;
    }

    private function errorImportLog($error)
    {
        $handle = fopen($this->logPath, 'ab');
        fwrite($handle, $error . "\n");
        if ($this->output) {
            $this->output->writeln($error . "\n");
        }
        fclose($handle);
    }

    /**
     * @param array $array
     *
     * @return string
     */
    private function arrayForQueryTransformFarming($array)
    {
        foreach ($array as $key => $value) {
            $array[$key] = '\'' . $value['name'] . '\'';
        }

        return implode(', ', $array);
    }

    private function countIndexes()
    {
        $initialMaxContractId = $this->maxID('SELECT max(id) FROM su_contracts');
        $initialContractsRentsId = $this->maxID('SELECT max(id) FROM su_contracts_rents');
        $initialContractPlotsId = $this->maxID('SELECT max(id) FROM su_contracts_plots_rel');
        $initialOwnersId = $this->maxID('SELECT max(id) FROM su_owners');
        $initialOwnersRepsId = $this->maxID('SELECT max(id) FROM su_owners_reps');
        $initialPlotsOwnersRelId = $this->maxID('SELECT max(id) FROM su_plots_owners_rel');
        $initialHypothecsId = $this->maxID('SELECT max(id) FROM su_hypothecs');
        $initialHypothecsPlotRelId = $this->maxID('SELECT max(id) FROM su_hypothecs_plots_rel');

        $this->errorImportLog('Initial id on table su_contracts           : ' . $initialMaxContractId);
        $this->errorImportLog('Initial id on table su_contracts_rents     : ' . $initialContractsRentsId);
        $this->errorImportLog('Initial id on table su_contracts_plots_rel : ' . $initialContractPlotsId);
        $this->errorImportLog('Initial id on table su_owners              : ' . $initialOwnersId);
        $this->errorImportLog('Initial id on table su_owners_reps         : ' . $initialOwnersRepsId);
        $this->errorImportLog('Initial id on table su_plots_owners_rel    : ' . $initialPlotsOwnersRelId);
        $this->errorImportLog('Initial id on table su_hypothecs           : ' . $initialHypothecsId);
        $this->errorImportLog('Initial id on table su_hypothecs_plots_rel : ' . $initialHypothecsPlotRelId);
    }

    private function maxID($sql)
    {
        $result = $this->userPDO->query($sql)->fetchAll();

        return (null == $result[0][0]) ? 0 : $result[0][0];
    }

    /**
     * @param string $name
     *
     * @throws Exception
     *
     * @return null|array
     */
    private function getRentаTypeByName($name)
    {
        $selectRentaType = $this->userPDO->prepare('SELECT id, name, unit, unit_value from su_renta_types where trim(lower(name)) = :name');

        $selectRentaType->bindValue(':name', trim(mb_strtolower($name, 'UTF-8')));
        $selectRentaType->execute();
        $rentaType = $selectRentaType->fetch(PDO::FETCH_ASSOC);

        if (empty($rentaType)) {
            throw new Exception('WARNING: Missing renta type for : ' . $name);
        }

        return $rentaType;
    }
}
