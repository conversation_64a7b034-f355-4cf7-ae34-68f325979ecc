<?php

namespace TF\Commands\Common;

use PDO;
use PDOException;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class CopyUserAccountCommand extends BaseCommand
{
    protected $skipLogging = true;

    protected OutputInterface $output;

    protected $destDbConnection;
    protected $sourceDbConnection;
    protected $userDbConnection;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:copy_user_account')
            ->setDescription('Copy a user account from production.')
            ->addArgument('user_db', InputArgument::REQUIRED, 'The user db name')
            ->addOption('source_db_host', 'D', InputOption::VALUE_OPTIONAL, 'Source db address', (getenv('COPY_USER_SOURCE_DB_HOST') ?: ''))
            ->addOption('source_db_port', 'P', InputOption::VALUE_OPTIONAL, 'Source database server port', (getenv('COPY_USER_SOURCE_DB_PORT') ?: ''))
            ->addOption('source_db_user', 'U', InputOption::VALUE_OPTIONAL, 'Source database user', (getenv('COPY_USER_SOURCE_DB_USER') ?: ''))
            ->addOption('source_db_password', 'W', InputOption::VALUE_OPTIONAL, 'Source server password', (getenv('COPY_USER_SOURCE_DB_PASS') ?: ''))
            ->addOption('source_main_db', 'M', InputOption::VALUE_OPTIONAL, 'Source server susi_main name', 'susi_main')
            ->addOption('main_parent', 'm', InputOption::VALUE_OPTIONAL, 'The id of the main parent account', 193)
            ->addOption('dest_db_host', 'd', InputOption::VALUE_OPTIONAL, 'Destination db address', (getenv('DEFAULT_DB_HOST') ?: ''))
            ->addOption('dest_db_port', 'p', InputOption::VALUE_OPTIONAL, 'Destination database server port', (getenv('DEFAULT_DB_PORT') ?: ''))
            ->addOption('dest_db_user', 'u', InputOption::VALUE_OPTIONAL, 'Destination database user', (getenv('DEFAULT_DB_USERNAME') ?: ''))
            ->addOption('dest_db_password', 'w', InputOption::VALUE_OPTIONAL, 'Destination server password', (getenv('DEFAULT_DB_PASSWORD') ?: ''))
            ->addOption('dest_main_db', 'E', InputOption::VALUE_OPTIONAL, 'Destination server susi main name', 'susi_main')
            ->addOption('dest_dblink_host', 'b', InputOption::VALUE_OPTIONAL, 'Destination dblink host', '')
            ->addOption('ssh_host', 's', InputOption::VALUE_OPTIONAL, 'SSH Host address', (getenv('COPY_USER_COMMAND_SSH_HOST') ?: ''))
            ->addOption('ssh_user', 'S', InputOption::VALUE_OPTIONAL, 'SSH user', (getenv('COPY_USER_COMMAND_SSH_USER') ?: ''))
            ->addOption('ssh_key_path', 'k', InputOption::VALUE_OPTIONAL, 'SSH key path', '')
            ->addOption('dest_pod_name', 'o', InputOption::VALUE_OPTIONAL, 'Destination Pod name', 'tf-technofarm-76455995f-62g89')
            ->addOption('kube_config_path', 'c', InputOption::VALUE_OPTIONAL, 'Kube config path', '/var/www/html/app/config/jwt/kube');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->output = $output;

        $username = $input->getArgument('user_db');
        $sourceDbHost = $input->getOption('source_db_host');
        $sourceDbUser = $input->getOption('source_db_user');
        $sourceDbPort = $input->getOption('source_db_port');
        $sourceDbPass = $input->getOption('source_db_password');
        $sourceMainDb = $input->getOption('source_main_db');
        $mainParent = $input->getOption('main_parent');
        $destDbHost = $input->getOption('dest_db_host');
        $destDbPort = $input->getOption('dest_db_port');
        $destDbUser = $input->getOption('dest_db_user');
        $destDbPass = $input->getOption('dest_db_password');
        $destMainDb = $input->getOption('dest_main_db');
        $sshHost = $input->getOption('ssh_host');
        $sshUser = $input->getOption('ssh_user');
        $destDBLinkHost = $input->getOption('dest_dblink_host');
        $sshKeyPath = $input->getOption('ssh_key_path');
        $destPodName = $input->getOption('dest_pod_name');
        $kubeConfigPath = $input->getOption('kube_config_path');

        $user_db = 'db_' . $username;

        if (!isset($user_db)) {
            $this->output->error('NO DATABASE');
            die;
        }

        if (empty($sourceDbHost)
            || empty($sourceDbUser)
            || empty($sourceDbPort)
            || empty($sourceDbPass)
            || empty($sourceMainDb)
            || empty($mainParent)
            || empty($destDbHost)
            || empty($destDbPort)
            || empty($destDbUser)
            || empty($destDbPass)
            || empty($destMainDb)
        ) {
            $this->output->error('Some parameter missing. Please check your command parameters or env variables');
            die;
        }

        try {
            if (!empty($sshHost)) {
                $sshCommand = 'ssh ';
                if (!empty($sshKeyPath)) {
                    $sshCommand .= ' -i ' . $sshKeyPath;
                }
                shell_exec($sshCommand . ' -f -L 127.0.0.1:3309:127.0.0.1:' . $sourceDbPort . ' ' . $sshUser . '@' . $sshHost . ' sleep 60 >> logfile');
                $this->sourceDbConnection = new PDO('pgsql:dbname=' . $sourceMainDb . ';host=127.0.0.1;port=3309;user=' . $sourceDbUser, $sourceDbUser, $sourceDbPass);
            } else {
                $this->sourceDbConnection = new PDO('pgsql:dbname=' . $sourceMainDb . ';host=' . $sourceDbHost . ';port=' . $sourceDbPort . ';user=' . $sourceDbUser, $sourceDbUser, $sourceDbPass);
            }

            $this->destDbConnection = new PDO('pgsql:dbname=' . $destMainDb . ';host=' . $destDbHost . ';port=' . $destDbPort . ';user=' . $destDbUser, $destDbUser, $destDbPass);
        } catch (PDOException $e) {
            $this->output->error($e->getMessage());
            exit;
        }

        $estmt = $this->destDbConnection->query("SELECT * FROM su_users u WHERE u.database = '" . $user_db . "';");
        $existingUser = $estmt->fetch();

        if (!empty($existingUser)) {
            $this->output->info('User with DB ' . $user_db . ' already exists in destination data base');
            $queries = [
                "DELETE FROM su_requested_ekattes WHERE group_id = {$existingUser['group_id']};",
                "DELETE FROM su_users WHERE level != 1 and group_id = {$existingUser['group_id']};",
                "DELETE FROM su_users_farming WHERE group_id = {$existingUser['group_id']};",
                "DELETE FROM su_system_users WHERE user_id = {$existingUser['id']};",
                "DELETE FROM su_users_files WHERE group_id = {$existingUser['group_id']};",
                "DELETE FROM su_users_layers WHERE group_id = {$existingUser['group_id']};",
                "DELETE FROM su_users_agreements WHERE user_id = {$existingUser['id']};",
                "DELETE FROM su_users_coverage WHERE group_id = {$existingUser['group_id']};",
                "DELETE FROM su_users_croplayers WHERE group_id = {$existingUser['group_id']};",
                "DELETE FROM su_users_overlaps WHERE user_id = {$existingUser['id']};",
                "DELETE FROM su_users_rights WHERE user_id = {$existingUser['group_id']};",
                "DELETE FROM su_object_permissions WHERE user_id = {$existingUser['id']};",
            ];

            foreach ($queries as $query) {
                $this->output->info($query);
                $destDbConnectionCmd = $this->destDbConnection->prepare($query);
                if (!$destDbConnectionCmd->execute()) {
                    $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
                    echo 'Line: ' . __LINE__ . PHP_EOL;
                    exit;
                }
            }
        }

        $stmt = $this->sourceDbConnection->query("SELECT * FROM su_users u WHERE u.username = '" . $username . "';");
        $user = $stmt->fetch();
        if (empty($user)) {
            $this->output->info('User: ' . $username . ' not found');
        }

        $this->output->info('User ' . $user['username'] . ' was found');

        $stmt = $this->sourceDbConnection->query("SELECT * FROM su_users_farming f WHERE f.group_id = '" . $user['group_id'] . "';");
        $userFarms = $stmt->fetchAll();

        if (empty($user)) {
            $this->output->writeln('User farms not found');
        }

        $stmt = $this->sourceDbConnection->query("SELECT * FROM su_users_files f WHERE f.group_id = '" . $user['group_id'] . "';");
        $userFiles = $stmt->fetchAll();

        $stmt = $this->sourceDbConnection->query("SELECT * FROM su_users_layers l WHERE l.group_id = '" . $user['group_id'] . "';");
        $userLayers = $stmt->fetchAll();

        $stmt = $this->sourceDbConnection->query("SELECT * FROM su_users_agreements ag WHERE ag.user_id = '" . $user['id'] . "';");
        $userAgreements = $stmt->fetchAll();

        $stmt = $this->sourceDbConnection->query("SELECT * FROM su_users_coverage co WHERE co.group_id = '" . $user['group_id'] . "';");
        $userCoverage = $stmt->fetchAll();

        $stmt = $this->sourceDbConnection->query("SELECT * FROM su_users_croplayers cl WHERE cl.group_id = '" . $user['group_id'] . "';");
        $userCropLayers = $stmt->fetchAll();

        $stmt = $this->sourceDbConnection->query("SELECT * FROM su_users_overlaps ol WHERE ol.user_id = '" . $user['id'] . "';");
        $userOverLaps = $stmt->fetchAll();

        $stmt = $this->sourceDbConnection->query("SELECT * FROM su_users_rights ur WHERE ur.user_id = '" . $user['id'] . "';");
        $userRights = $stmt->fetchAll();

        $stmt = $this->sourceDbConnection->query("SELECT * FROM su_object_permissions up WHERE up.user_id = '" . $user['id'] . "';");
        $userPermissions = $stmt->fetchAll();

        $stmt = $this->sourceDbConnection->query("SELECT * FROM su_users u WHERE u.level != 1 and u.id != '" . $user['id'] . "' and u.group_id = '" . $user['group_id'] . "';");
        $userChildren = $stmt->fetchAll();

        $userChildrenRights = [];
        $userChildrenPermissions = [];
        foreach ($userChildren as $userChild) {
            $stmt = $this->sourceDbConnection->query("SELECT * FROM su_users_rights ur WHERE ur.user_id = '" . $userChild['id'] . "';");
            $userChildrenRights[$userChild['username']] = $stmt->fetchAll();

            $stmt = $this->sourceDbConnection->query("SELECT * FROM su_object_permissions up WHERE up.user_id = '" . $userChild['id'] . "';");
            $userChildrenPermissions[$userChild['username']] = $stmt->fetchAll();
        }

        $this->output->info('Adding new users');

        $sql = 'INSERT INTO su_users(username,password,name,address,phone,email,comment,is_superadmin,
                                database,hash,parent_id,can_create,level,group_id,active,server,start_date,due_date,entry_flag,entries_left,date_flag,map_type,is_trial,
                                allowed_farmings,track_username,track_password,creation_date,last_login_date, last_login_ip, paid_support,
                                app_version, app_critical_upd, track_token, login_token, salesperson, salesperson_id, ekatte_count,
                                total_plot_area, paid_support_start_date, paid_support_due_date, waiting_gs_integration, identity_number, create_cms_contracts, keycloak_uid 
                            )
                            VALUES(:username, :password, :name, :address, :phone, :email, :comment, :is_superadmin, :database, :hash, :parent_id, :can_create,
                                :level, :group_id, :active, :server, :start_date, :due_date, :entry_flag, :entries_left, :date_flag, :map_type, :is_trial,
                                :allowed_farmings, :track_username, :track_password, :creation_date,:last_login_date, :last_login_ip, :paid_support,
                                :app_version, :app_critical_upd, :track_token, :login_token, :salesperson, :salesperson_id, :ekatte_count,
                                :total_plot_area, :paid_support_start_date, :paid_support_due_date, :waiting_gs_integration, :identity_number, :create_cms_contracts, :keycloak_uid)';

        $destDbConnectionCmd = $this->destDbConnection->prepare($sql);

        $destDbConnectionCmd->bindParam(':username', $user['username']);
        $destDbConnectionCmd->bindParam(':password', $user['password']);
        $destDbConnectionCmd->bindParam(':name', $user['name']);
        $destDbConnectionCmd->bindParam(':address', $user['address']);
        $destDbConnectionCmd->bindParam(':phone', $user['phone']);
        $destDbConnectionCmd->bindParam(':email', $user['email']);
        $destDbConnectionCmd->bindParam(':comment', $user['comment']);
        $destDbConnectionCmd->bindValue(':is_superadmin', ($user['is_superadmin'] ? true : false), PDO::PARAM_BOOL);
        $destDbConnectionCmd->bindParam(':database', $user['database']);
        $destDbConnectionCmd->bindParam(':hash', $user['hash']);
        $destDbConnectionCmd->bindParam(':parent_id', $mainParent);
        $destDbConnectionCmd->bindValue(':can_create', 10);
        $destDbConnectionCmd->bindParam(':level', $user['level']);
        $destDbConnectionCmd->bindParam(':group_id', $user['group_id']);
        $destDbConnectionCmd->bindValue(':active', ($user['active'] ? true : false), PDO::PARAM_BOOL);
        $destDbConnectionCmd->bindParam(':server', $user['server']);
        $destDbConnectionCmd->bindValue(':start_date', ($user['start_date'] ? $user['start_date'] : '1970-01-01'));
        $destDbConnectionCmd->bindValue(':due_date', ($user['start_date'] ? $user['due_date'] : '1970-01-01'));
        $destDbConnectionCmd->bindValue(':entry_flag', ($user['entry_flag'] ? $user['entry_flag'] : 0));
        $destDbConnectionCmd->bindValue(':entries_left', ($user['entries_left'] ? $user['entries_left'] : 0));
        $destDbConnectionCmd->bindValue(':date_flag', ($user['date_flag'] ? $user['date_flag'] : 0));
        $destDbConnectionCmd->bindValue(':map_type', ($user['map_type'] ? $user['map_type'] : 1));
        $destDbConnectionCmd->bindValue(':is_trial', ($user['is_trial'] ? $user['is_trial'] : false), PDO::PARAM_BOOL);
        $destDbConnectionCmd->bindValue(':allowed_farmings', 10);
        $destDbConnectionCmd->bindParam(':track_username', $user['track_username']);
        $destDbConnectionCmd->bindParam(':track_password', $user['track_password']);
        $destDbConnectionCmd->bindParam(':creation_date', $user['creation_date']);
        $destDbConnectionCmd->bindParam(':last_login_date', $user['last_login_date']);
        $destDbConnectionCmd->bindParam(':last_login_ip', $user['last_login_ip']);
        $destDbConnectionCmd->bindParam(':paid_support', $user['paid_support']);
        $destDbConnectionCmd->bindParam(':app_version', $user['app_version']);
        $destDbConnectionCmd->bindValue(':app_critical_upd', ($user['app_critical_upd'] ? $user['app_critical_upd'] : false), PDO::PARAM_BOOL);
        $destDbConnectionCmd->bindParam(':track_token', $user['track_token']);
        $destDbConnectionCmd->bindParam(':login_token', $user['login_token']);
        $destDbConnectionCmd->bindParam(':salesperson', $user['salesperson']);
        $destDbConnectionCmd->bindParam(':salesperson_id', $user['salesperson_id']);
        $destDbConnectionCmd->bindParam(':ekatte_count', $user['ekatte_count']);
        $destDbConnectionCmd->bindParam(':total_plot_area', $user['total_plot_area']);
        $destDbConnectionCmd->bindParam(':paid_support_start_date', $user['paid_support_start_date']);
        $destDbConnectionCmd->bindParam(':paid_support_due_date', $user['paid_support_due_date']);
        $destDbConnectionCmd->bindParam(':identity_number', $user['identity_number']);
        $destDbConnectionCmd->bindParam(':waiting_gs_integration', $user['waiting_gs_integration'], PDO::PARAM_BOOL);
        $destDbConnectionCmd->bindParam(':create_cms_contracts', $user['create_cms_contracts'], PDO::PARAM_BOOL);
        $destDbConnectionCmd->bindParam(':keycloak_uid', $user['keycloak_uid']);

        if (!$destDbConnectionCmd->execute()) {
            $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
            echo 'Line: ' . __LINE__ . PHP_EOL;
            exit;
        }

        $userId = $this->destDbConnection->lastInsertId('su_users_id_seq');

        $sql = 'UPDATE su_users SET group_id = :group_id WHERE id = :user_id';
        $destDbConnectionCmd = $this->destDbConnection->prepare($sql);
        $destDbConnectionCmd->bindValue(':user_id', $userId);
        $destDbConnectionCmd->bindValue(':group_id', $userId);

        if (!$destDbConnectionCmd->execute()) {
            $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
            echo 'Line: ' . __LINE__ . PHP_EOL;
            exit;
        }

        $this->output->info('User inserted: ' . $user['username']);
        $this->output->info('Added user id: ' . $userId);

        if (!$destDbConnectionCmd->execute()) {
            $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
            echo 'Line: ' . __LINE__ . PHP_EOL;
            exit;
        }

        $this->output->info('System user inserted: ' . $user['username']);

        $sql = 'INSERT INTO su_users_rights(user_id, right_id, group_id)
                                    VALUES(:user_id, :right_id, :group_id)';
        $destDbConnectionCmd = $this->destDbConnection->prepare($sql);

        foreach ($userRights as $userRight) {
            $destDbConnectionCmd->bindValue(':user_id', $userId);
            $destDbConnectionCmd->bindValue(':right_id', $userRight['right_id']);
            $destDbConnectionCmd->bindValue(':group_id', $userId); // parent_id

            if (!$destDbConnectionCmd->execute()) {
                $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
                echo 'Line: ' . __LINE__ . PHP_EOL;
                exit;
            }
        }

        $this->output->info('User Rights are successfully inserted.');

        $sql = 'INSERT INTO su_users(username,password,name,address,phone,email,comment,is_superadmin,
                                database,hash,parent_id,can_create,level,group_id,active,server,start_date,due_date,entry_flag,entries_left,date_flag,map_type,is_trial,
                                allowed_farmings,track_username,track_password,creation_date,last_login_date, last_login_ip, paid_support,
                                app_version, track_token, login_token, salesperson, salesperson_id, ekatte_count,
                                total_plot_area, paid_support_start_date, paid_support_due_date, keycloak_uid
                                        )
                                VALUES(:username, :password, :name, :address, :phone, :email, :comment, :is_superadmin, :database, :hash, :parent_id, :can_create,
                                :level, :group_id, :active, :server, :start_date, :due_date, :entry_flag, :entries_left, :date_flag, :map_type, :is_trial,
                                :allowed_farmings, :track_username, :track_password, :creation_date,:last_login_date, :last_login_ip, :paid_support,
                                :app_version, :track_token, :login_token, :salesperson, :salesperson_id, :ekatte_count,
                                :total_plot_area, :paid_support_start_date, :paid_support_due_date, :keycloak_uid)';
        $destDbConnectionCmd = $this->destDbConnection->prepare($sql);
        foreach ($userChildren as $userChild) {
            $destDbConnectionCmd->bindValue(':username', $userChild['username']);
            $destDbConnectionCmd->bindValue(':password', $userChild['password']);
            $destDbConnectionCmd->bindValue(':name', $userChild['name']);
            $destDbConnectionCmd->bindValue(':address', $userChild['address']);
            $destDbConnectionCmd->bindValue(':phone', $userChild['phone']);
            $destDbConnectionCmd->bindValue(':email', $userChild['email']);
            $destDbConnectionCmd->bindValue(':comment', $userChild['comment']);
            $destDbConnectionCmd->bindValue(':is_superadmin', ($userChild['is_superadmin'] ? 1 : 0));
            $destDbConnectionCmd->bindValue(':database', $userChild['database']);
            $destDbConnectionCmd->bindValue(':hash', $userChild['hash']);
            $destDbConnectionCmd->bindValue(':parent_id', $userId);
            $destDbConnectionCmd->bindValue(':can_create', $userChild['can_create']);
            $destDbConnectionCmd->bindValue(':level', $userChild['level']);
            $destDbConnectionCmd->bindValue(':group_id', $userId);
            $destDbConnectionCmd->bindValue(':active', ($userChild['active'] ? 1 : 0));
            $destDbConnectionCmd->bindValue(':server', $userChild['server']);
            $destDbConnectionCmd->bindValue(':start_date', ($userChild['start_date'] ? $userChild['start_date'] : '1970-01-01'));
            $destDbConnectionCmd->bindValue(':due_date', ($userChild['start_date'] ? $userChild['due_date'] : '1970-01-01'));
            $destDbConnectionCmd->bindValue(':entry_flag', ($userChild['entry_flag'] ? $userChild['entry_flag'] : 0));
            $destDbConnectionCmd->bindValue(':entries_left', ($userChild['entries_left'] ? $userChild['entries_left'] : 0));
            $destDbConnectionCmd->bindValue(':date_flag', ($userChild['date_flag'] ? $userChild['date_flag'] : 0));
            $destDbConnectionCmd->bindValue(':map_type', ($userChild['map_type'] ? $userChild['map_type'] : 1));
            $destDbConnectionCmd->bindValue(':is_trial', ($userChild['is_trial'] ? 1 : 0));
            $destDbConnectionCmd->bindValue(':allowed_farmings', ($userChild['allowed_farmings'] ? $userChild['allowed_farmings'] : 1));
            $destDbConnectionCmd->bindValue(':track_username', $userChild['track_username']);
            $destDbConnectionCmd->bindValue(':track_password', $userChild['track_password']);
            $destDbConnectionCmd->bindValue(':creation_date', $userChild['creation_date']);
            $destDbConnectionCmd->bindParam(':last_login_date', $user['last_login_date']);
            $destDbConnectionCmd->bindParam(':last_login_ip', $user['last_login_ip']);
            $destDbConnectionCmd->bindParam(':paid_support', $user['paid_support']);
            $destDbConnectionCmd->bindParam(':app_version', $user['app_version']);
            $destDbConnectionCmd->bindParam(':track_token', $user['track_token']);
            $destDbConnectionCmd->bindParam(':login_token', $user['login_token']);
            $destDbConnectionCmd->bindParam(':salesperson', $user['salesperson']);
            $destDbConnectionCmd->bindParam(':salesperson_id', $user['salesperson_id']);
            $destDbConnectionCmd->bindParam(':ekatte_count', $user['ekatte_count']);
            $destDbConnectionCmd->bindParam(':total_plot_area', $user['total_plot_area']);
            $destDbConnectionCmd->bindParam(':paid_support_start_date', $user['paid_support_start_date']);
            $destDbConnectionCmd->bindParam(':paid_support_due_date', $user['paid_support_due_date']);
            $destDbConnectionCmd->bindParam(':keycloak_uid', $userChild['keycloak_uid']);

            if (!$destDbConnectionCmd->execute()) {
                $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
                echo 'Line: ' . __LINE__ . PHP_EOL;
                exit;
            }

            $userChildrenMap[$userChild['id']] = $this->destDbConnection->lastInsertId('su_users_id_seq');
        }

        if (!empty($userChildrenRights)) {
            $sql = 'INSERT INTO su_users_rights(user_id, right_id, group_id) VALUES (:user_id1, :user_id2, :group_id)';
            $destDbConnectionCmd = $this->destDbConnection->prepare($sql);

            foreach ($userChildrenRights as $childUser => $userChildRights) {
                foreach ($userChildRights as $userChildRight) {
                    $destDbConnectionCmd->bindValue(':user_id1', $userChildrenMap[$userChildRight['user_id']]);
                    $destDbConnectionCmd->bindValue(':user_id2', $userChildRight['right_id']);
                    $destDbConnectionCmd->bindValue(':group_id', $userId);

                    if (!$destDbConnectionCmd->execute()) {
                        $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
                        echo 'Line: ' . __LINE__ . PHP_EOL;
                        exit;
                    }
                }

                $this->output->info('Rights for child ' . $childUser . ' was inserted');
            }
        }

        $sql = 'INSERT INTO su_users_agreements(user_id, database, item_id, status)
                                    VALUES (:user_id, :database, :item_id, :status)';
        $destDbConnectionCmd = $this->destDbConnection->prepare($sql);
        foreach ($userAgreements as $agreement) {
            $destDbConnectionCmd->bindValue(':user_id', $userId);
            $destDbConnectionCmd->bindValue(':database', $agreement['database']);
            $destDbConnectionCmd->bindValue(':item_id', $agreement['item_id']);
            $destDbConnectionCmd->bindValue(':status', $agreement['status'], PDO::PARAM_INT);

            if (!$destDbConnectionCmd->execute()) {
                $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
                echo 'Line: ' . __LINE__ . PHP_EOL;
                exit;
            }

            $this->output->info('agreements inserted: ' . $agreement['database']);
        }

        if (!empty($userCoverage)) {
            $sql = 'INSERT INTO su_users_coverage(upload_date, filename, group_id, user_id, item_id, status, database)
                                        VALUES (:upload_date, :filename, :group_id, :user_id, :item_id, :status, :database)';
            $destDbConnectionCmd = $this->destDbConnection->prepare($sql);
            foreach ($userCoverage as $coverage) {
                $destDbConnectionCmd->bindValue(':upload_date', $coverage['upload_date']);
                $destDbConnectionCmd->bindValue(':filename', $coverage['filename']);
                $destDbConnectionCmd->bindValue(':group_id', $userId);
                $destDbConnectionCmd->bindValue(':user_id', $userId);
                $destDbConnectionCmd->bindValue(':item_id', $coverage['item_id']);
                $destDbConnectionCmd->bindValue(':status', $coverage['status'], PDO::PARAM_INT);
                $destDbConnectionCmd->bindValue(':database', $coverage['database']);

                if (!$destDbConnectionCmd->execute()) {
                    $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
                    echo 'Line: ' . __LINE__ . PHP_EOL;
                    exit;
                }

                $this->output->info('Covarage inserted: ' . $coverage['filename']);
            }
        }

        if (!empty($croplayers)) {
            $sql = 'INSERT INTO su_users_croplayers(user_id, status, database, item_id, group_id)
                                    VALUES (:user_id, :status, :database, :item_id, :group_id)';
            $destDbConnectionCmd = $this->destDbConnection->prepare($sql);
            foreach ($userCropLayers as $userCropLayer) {
                $destDbConnectionCmd->bindValue(':user_id', $userId);
                $destDbConnectionCmd->bindValue(':status', $userCropLayer['status'], PDO::PARAM_INT);
                $destDbConnectionCmd->bindValue(':database', $userCropLayer['database']);
                $destDbConnectionCmd->bindValue(':item_id', $userCropLayer['item_id']);
                $destDbConnectionCmd->bindValue(':group_id', $userId);
                $destDbConnectionCmd->execute();

                $this->output->info('Croplayers inserted: ' . $userCropLayer['database']);
            }
        }

        $farmingRel = [];
        $sql = 'INSERT INTO su_users_farming(user_id, uuid, name, is_system, company, bulstat, group_id, address, company_address, mol, ao_db)
                                    VALUES(:user_id, :uuid, :name, :is_system, :company, :bulstat, :group_id, :address, :company_address, :mol, :ao_db)';
        $destDbConnectionCmd = $this->destDbConnection->prepare($sql);
        foreach ($userFarms as $userFarm) {
            $destDbConnectionCmd->bindValue(':user_id', $userId, PDO::PARAM_INT);
            $destDbConnectionCmd->bindValue(':uuid', $userFarm['uuid'], PDO::PARAM_STR);
            $destDbConnectionCmd->bindValue(':name', $userFarm['name']);
            $destDbConnectionCmd->bindValue(':is_system', ($userFarm['is_system'] ? true : false), PDO::PARAM_BOOL);
            $destDbConnectionCmd->bindValue(':company', $userFarm['company']);
            $destDbConnectionCmd->bindValue(':bulstat', $userFarm['bulstat']);
            $destDbConnectionCmd->bindValue(':group_id', $userId, PDO::PARAM_INT);
            $destDbConnectionCmd->bindValue(':address', $userFarm['address']);
            $destDbConnectionCmd->bindValue(':company_address', $userFarm['company_address']);
            $destDbConnectionCmd->bindValue(':mol', $userFarm['mol']);
            $destDbConnectionCmd->bindValue(':ao_db', $userFarm['ao_db']);

            if (!$destDbConnectionCmd->execute()) {
                $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
                echo 'Line: ' . __LINE__ . PHP_EOL;
                exit;
            }

            $lastFarmId = $this->destDbConnection->lastInsertId('su_users_farming_id_seq');

            $this->output->info('Farm inserted: ' . $userFarm['name']);
            $farmingRel[$userFarm['id']] = $lastFarmId;
        }

        $sql = 'INSERT INTO su_object_permissions(class, user_id, object_id, permission) VALUES (:class, :user_id, :object_id, :permission)';
        $destDbConnectionCmd = $this->destDbConnection->prepare($sql);

        foreach ($userPermissions as $userPermission) {
            if ($farmingRel[$userPermission['object_id']]) {
                $destDbConnectionCmd->bindValue(':class', $userPermission['class']);
                $destDbConnectionCmd->bindValue(':user_id', $userId);
                $destDbConnectionCmd->bindValue(':object_id', $farmingRel[$userPermission['object_id']]);
                $destDbConnectionCmd->bindValue(':permission', $userPermission['permission']);

                if (!$destDbConnectionCmd->execute()) {
                    $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
                    echo 'Line: ' . __LINE__ . PHP_EOL;
                    exit;
                }
            }
        }

        $this->output->info('User Permissions are successfully inserted.');

        if (!empty($userChildrenPermissions)) {
            $sql = 'INSERT INTO su_object_permissions(class, user_id, object_id, permission) VALUES (:class, :user_id, :object_id, :permission)';
            $destDbConnectionCmd = $this->destDbConnection->prepare($sql);
            foreach ($userChildrenPermissions as $childUser => $userChildPermissions) {
                foreach ($userChildPermissions as $userChildPermission) {
                    if (empty($userChildrenMap[$userChildPermission['user_id']]) || empty($farmingRel[$userChildPermission['object_id']])) {
                        continue;
                    }
                    $destDbConnectionCmd->bindValue(':class', $userChildPermission['class']);
                    $destDbConnectionCmd->bindValue(':user_id', $userChildrenMap[$userChildPermission['user_id']]);
                    $destDbConnectionCmd->bindValue(':object_id', $farmingRel[$userChildPermission['object_id']]);
                    $destDbConnectionCmd->bindValue(':permission', $userChildPermission['permission']);

                    if (!$destDbConnectionCmd->execute()) {
                        $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
                        echo 'Line: ' . __LINE__ . PHP_EOL;
                        exit;
                    }
                }

                $this->output->info('Permissions for child ' . $childUser . ' was inserted');
            }
        }

        if (!empty($userFiles)) {
            $sql = 'INSERT INTO su_users_files(filename, date_uploaded, status, errors, farming, year, user_id, name, crs, shape_type, definition, group_id, device_type)
                                    VALUES(:filename, :date_uploaded, :status, :errors, :farming, :year, :user_id, :name, :crs, :shape_type, :definition, :group_id, :device_type)';
            $destDbConnectionCmd = $this->destDbConnection->prepare($sql);
            foreach ($userFiles as $userFile) {
                $destDbConnectionCmd->bindValue(':filename', $userFile['filename']);
                $destDbConnectionCmd->bindValue(':date_uploaded', $userFile['date_uploaded']);
                $destDbConnectionCmd->bindValue(':status', $userFile['status']);
                $destDbConnectionCmd->bindValue(':errors', $userFile['errors']);
                $destDbConnectionCmd->bindValue(':farming', ($userFile['farming'] ? $farmingRel[$userFile['farming']] : null));
                $destDbConnectionCmd->bindValue(':year', ($userFile['year'] ? $userFile['year'] : null));
                $destDbConnectionCmd->bindValue(':user_id', $userId);
                $destDbConnectionCmd->bindValue(':name', $userFile['name']);
                $destDbConnectionCmd->bindValue(':crs', $userFile['crs']);
                $destDbConnectionCmd->bindValue(':shape_type', $userFile['shape_type']);
                $destDbConnectionCmd->bindValue(':definition', $userFile['definition']);
                $destDbConnectionCmd->bindValue(':group_id', $userId);
                $destDbConnectionCmd->bindValue(':device_type', $userFile['device_type']);

                if (!$destDbConnectionCmd->execute()) {
                    $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
                    echo 'Line: ' . __LINE__ . PHP_EOL;
                    exit;
                }

                $this->output->info('File inserted: ' . $userFile['name']);
            }
        }

        $this->output->info('Inserting user layers...');
        $sql = 'INSERT INTO su_users_layers(user_id, name, table_name, date_created, color, border_color, extent, farming, year, transparency, position, layer_type, group_id, tags, border_only, is_exist, label_name, style, definitions, is_from_ao_migration)
                    VALUES(:user_id, :name, :table_name, :date_created, :color, :border_color, :extent, :farming, :year, :transparency, :position, :layer_type, :group_id, :tags, :border_only, :is_exist, :label_name, :style, :definitions, :is_from_ao_migration)';
        $destDbConnectionCmd = $this->destDbConnection->prepare($sql);
        foreach ($userLayers as $userLayer) {
            $destDbConnectionCmd->bindValue(':user_id', $userId);
            $destDbConnectionCmd->bindValue(':name', $userLayer['name']);
            $destDbConnectionCmd->bindValue(':table_name', $userLayer['table_name']);
            $destDbConnectionCmd->bindValue(':date_created', $userLayer['date_created']);
            $destDbConnectionCmd->bindValue(':color', $userLayer['color']);
            $destDbConnectionCmd->bindValue(':border_color', $userLayer['border_color']);
            $destDbConnectionCmd->bindValue(':extent', $userLayer['extent']);
            $destDbConnectionCmd->bindValue(':farming', ($userLayer['farming'] ? $farmingRel[$userLayer['farming']] : null));
            $destDbConnectionCmd->bindValue(':year', $userLayer['year']);
            $destDbConnectionCmd->bindValue(':transparency', $userLayer['transparency']);
            $destDbConnectionCmd->bindValue(':position', $userLayer['position']);
            $destDbConnectionCmd->bindValue(':layer_type', $userLayer['layer_type']);
            $destDbConnectionCmd->bindValue(':group_id', $userId);
            $destDbConnectionCmd->bindValue(':tags', $userLayer['tags'], PDO::PARAM_BOOL);
            $destDbConnectionCmd->bindValue(':border_only', $userLayer['border_only'], PDO::PARAM_BOOL);
            $destDbConnectionCmd->bindValue(':is_exist', $userLayer['is_exist'], PDO::PARAM_BOOL);
            $destDbConnectionCmd->bindValue(':label_name', $userLayer['label_name']);
            $destDbConnectionCmd->bindValue(':style', $userLayer['style']);
            $destDbConnectionCmd->bindValue(':definitions', $userLayer['definitions']);
            $destDbConnectionCmd->bindValue(':is_from_ao_migration', $userLayer['is_from_ao_migration'], PDO::PARAM_BOOL);

            if (!$destDbConnectionCmd->execute()) {
                $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
                echo 'Line: ' . __LINE__ . PHP_EOL;
                exit;
            }
        }

        $this->output->info('User layers inserted');

        if ($userOverLaps) {
            $sql = 'INSERT INTO su_users_overlaps(user_id, database, item_id, status)
            VALUES(:user_id, :database, :item_id, :status)';
            $destDbConnectionCmd = $this->destDbConnection->prepare($sql);
            foreach ($userOverLaps as $userOverLap) {
                $destDbConnectionCmd->bindValue(':user_id', $userId);
                $destDbConnectionCmd->bindValue(':database', $userOverLap['database']);
                $destDbConnectionCmd->bindValue(':item_id', $userOverLap['item_id']);
                $destDbConnectionCmd->bindValue(':status', $userOverLap['status'], PDO::PARAM_INT);

                if (!$destDbConnectionCmd->execute()) {
                    $this->output->error('<pre>' . print_r($destDbConnectionCmd->errorInfo(), true) . '</pre>');
                    echo 'Line: ' . __LINE__ . PHP_EOL;
                    exit;
                }

                $this->output->info('Overlaps inserted: ' . $userOverLap['item_id']);
            }
        }

        $this->output->info('Start coping user data base');
        $destDBLinkHost = $destDBLinkHost ?: $destDbHost;
        $this->copyUserDatabase($user_db, $sourceDbHost, $sourceDbPort, $sourceDbUser, $sourceDbPass, $destDbHost, $destDbPort, $destDbUser, $destDbPass, $destDBLinkHost, $sshHost, $sshUser, $sshKeyPath);
        $this->output->info('User data base is succefully copied');

        if ('**************' == $destDbHost) {
            $podName = system('./kubectl --kubeconfig=' . $kubeConfigPath . ' get pods | grep tf-technofarm | grep -v crontab | grep -v mapcache | grep -v mapserver | grep -v nginx | awk \'{print $1}\'');
            system('./kubectl --kubeconfig=' . $kubeConfigPath . ' exec -it ' . $podName . ' php run.php tf:regenerate_map_files ' . $user_db);
            system('./kubectl --kubeconfig=' . $kubeConfigPath . ' exec -it ' . $podName . ' php run.php tf:add_missing_layers_columns_by_definitions -f ' . $user_db);
            system('./kubectl --kubeconfig=' . $kubeConfigPath . ' exec -it ' . $podName . ' -- php run.php tf:init_user_requested_ekattes');
            system('./kubectl --kubeconfig=' . $kubeConfigPath . ' exec -it ' . $podName . ' -- php run.php v4.2:GPS-4172-3 -f ' . $user_db);
            system('./kubectl --kubeconfig=' . $kubeConfigPath . ' exec -it ' . $podName . ' -- php run.php v4.2:GPS-4497 -f ' . $user_db);
        } else {
            $this->output->info('Add missing layer colums by definitions');
            system('php run.php tf:regenerate_map_files -f' . $user_db);
            system('php run.php tf:add_missing_layers_columns_by_definitions -f ' . $user_db);
            system('php run.php tf:init_user_requested_ekattes');
            system('php run.php v4.2:GPS-4172-3 -f' . $user_db);
            system('php run.php v4.2:GPS-4497 -f' . $user_db);
        }

        $this->output->info('Update farming id in the contract table.');
        $sql = 'UPDATE su_contracts SET farming_id = :new_farming_id WHERE farming_id = :old_user_id';
        $userDbConnectionCmd = $this->userDbConnection->prepare($sql);

        foreach ($farmingRel as $oldFarmId => $newFarmId) {
            $userDbConnectionCmd->bindValue(':new_farming_id', $newFarmId);
            $userDbConnectionCmd->bindValue(':old_user_id', $oldFarmId);

            if (!$userDbConnectionCmd->execute()) {
                $this->output->error('<pre>' . print_r($userDbConnectionCmd->errorInfo(), true) . '</pre>');
                echo 'Line: ' . __LINE__ . PHP_EOL;
                exit;
            }
        }

        $sql = "UPDATE su_contracts c
        SET farming_id = f.id
        FROM dblink (
            'host=" . $destDbHost . ' port=' . $destDbPort . ' dbname=susi_main user=' . $destDbUser . ' password=' . $destDbPass . "',
            'SELECT id, name, ao_db FROM su_users_farming WHERE true'
        ) AS f (id int, name varchar, ao_db varchar)
        WHERE c.ao_db is not null and c.ao_db = f.ao_db;";
        $userDbConnectionCmd = $this->userDbConnection->prepare($sql);
        $userDbConnectionCmd->execute();

        $this->output->info('Deleting layer_tmp_kvs_* tables');

        $stmt = $this->destDbConnection->query("SELECT table_name FROM INFORMATION_SCHEMA.tables WHERE TABLE_NAME LIKE 'layer_tmp_kvs_%';");
        $userTmpLayers = $stmt->fetchAll();

        foreach ($userTmpLayers as $userTmpLayer) {
            $sql = 'DROP TABLE ' . $userTmpLayer['table_name'];
            $userDbConnectionCmd = $this->destDbConnection->prepare($sql);
            $userDbConnectionCmd->execute();
        }
    }

    private function copyUserDatabase($dbName, $sourceHost, $sourcePort, $sourceDBUser, $sourceDBPass, $destHost, $destPort, $destDBUser, $destDBPass, $destDBLinkHost, $sshHost = null, $sshUser = null, $keyPath = null)
    {
        // remove file if exists
        $fileName = $dbName . '.sql';
        if (file_exists($fileName)) {
            unlink($fileName);
            $this->output->info('Deleted: ' . $fileName);
        }

        $this->output->info('Dumping: ' . $dbName . ' ...');
        if ($sshHost) {
            $sshCommand = 'ssh ';
            if (!empty($sshKeyPath)) {
                $sshCommand .= ' -i ' . $sshKeyPath;
            }

            system($sshCommand . ' ' . $sshUser . '@' . $sshHost . ' "pg_dump -U ' . $sourceDBUser . ' -h ' . $sourceHost . ' -p ' . $sourcePort . ' -d ' . $dbName . ' | gzip -9" > ' . $fileName . '.gz');
            system('gunzip ' . $fileName . '.gz');
        } else {
            system('PGPASSWORD=' . $sourceDBPass . ' pg_dump -U ' . $sourceDBUser . ' -h ' . $sourceHost . ' -p ' . $sourcePort . ' -s -d ' . $dbName . ' > schema_' . $fileName);
            system('PGPASSWORD=' . $sourceDBPass . ' pg_dump -Fc -U ' . $sourceDBUser . ' -h ' . $sourceHost . ' -p ' . $sourcePort . ' -a -d ' . $dbName . ' > ' . $fileName);
        }

        $this->output->info('Updating dblink params in materialized views');

        system('sed -i "s/host=[^ ]* port=/host=' . $destDBLinkHost . ' port=/g" schema_' . $fileName . '');
        system('sed -i "s/hostaddr=[^ ]* port=/hostaddr=' . $destDBLinkHost . ' port=/g" schema_' . $fileName . '');
        system('sed -i -E "s/port=[^ ]* dbname=/port=' . $destPort . ' dbname=/g" schema_' . $fileName . '');
        system('sed -i -E "s/user=[^ ]* password=/user=' . $destDBUser . ' password=/g" schema_' . $fileName . '');

        system('sed -i \'s/' . $sourceDBPass . '/' . $destDBPass . '/g\' schema_' . $fileName . '');

        $this->output->info('Finish updating dblink params in materialized views');

        $this->output->info('Termanating connections');
        system('PGPASSWORD=' . $destDBPass . ' psql -h ' . $destHost . ' -U ' . $destDBUser . ' -p ' . $destPort . ' -c "SELECT pid, pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = \'' . $dbName . '\' AND pid <> pg_backend_pid();"');

        $this->output->info('dropdb  -h ' . $destHost . ' -U ' . $destDBUser . ' -p ' . $destPort . ' ' . $dbName);
        system('PGPASSWORD=' . $destDBPass . ' dropdb  -h ' . $destHost . ' -U ' . $destDBUser . ' -p ' . $destPort . ' ' . $dbName);

        $this->output->info('Creating database ' . $dbName . ' on ' . $destHost);
        system('PGPASSWORD=' . $destDBPass . ' createdb -h ' . $destHost . ' -p ' . $destPort . ' -U ' . $destDBUser . ' -E UTF8 ' . $dbName . ';');

        try {
            $this->userDbConnection = new PDO("pgsql:dbname={$dbName};host=" . $destHost . ';port=' . $destPort . ';user=' . $destDBUser, $destDBUser, $destDBPass);
        } catch (PDOException $e) {
            $this->output->error($e->getMessage());
            exit;
        }

        $this->output->info('Restoring DB schema: ' . $dbName . ' ...');
        system('PGPASSWORD=' . $destDBPass . ' psql -h ' . $destHost . ' -U ' . $destDBUser . ' -p ' . $destPort . ' -d ' . $dbName . ' < schema_' . $fileName, $output);

        $this->output->info('Deleting materialized views: layer_decl_69_70_*');
        $sql = "DO $$ 
                DECLARE 
                    r RECORD;
                BEGIN
                    FOR r IN 
                        SELECT matviewname 
                        FROM pg_matviews 
                        WHERE matviewname LIKE 'layer_decl_69_70_%'
                    LOOP
                        EXECUTE 'DROP MATERIALIZED VIEW IF EXISTS ' || quote_ident(r.matviewname) || ' CASCADE';
                    END LOOP;
                END $$;
            ";
        $userDbConnectionCmd = $this->userDbConnection->prepare($sql);
        $userDbConnectionCmd->execute();

        $this->output->info('Deleting virtual colums: ' . $dbName . ' ...');
        $stmtVirtualCols = $this->userDbConnection->query("SELECT table_name, column_name 
                                                FROM information_schema.columns
                                                WHERE 
                                                    column_name LIKE 'virtual_%'
                                                    and generation_expression is not null");
        $tablesAndVirtualColums = $stmtVirtualCols->fetchAll();

        foreach ($tablesAndVirtualColums as $tableAndVirtualColum) {
            $sql = 'ALTER TABLE ' . $tableAndVirtualColum['table_name'] . ' DROP COLUMN ' . $tableAndVirtualColum['column_name'];
            $userDbConnectionCmd = $this->userDbConnection->prepare($sql);
            $userDbConnectionCmd->execute();
        }

        $this->output->info('Refreshing mat views: ' . $dbName . ' ...');
        $stmt = $this->userDbConnection->query('SELECT matviewname FROM pg_matviews;');
        $matViews = $stmt->fetchAll();
        foreach ($matViews as $matView) {
            $sql = 'REFRESH MATERIALIZED VIEW ' . $matView['matviewname'] . ' WITH DATA;';
            $userDbConnectionCmd = $this->userDbConnection->prepare($sql);
            $userDbConnectionCmd->execute();
        }

        $this->output->info('Restoring DB data: ' . $dbName . ' ...');
        system('PGPASSWORD=' . $destDBPass . ' pg_restore -Fc -U ' . $destDBUser . ' -h ' . $destHost . ' -p ' . $destPort . ' --disable-triggers -d ' . $dbName . ' ' . $fileName);

        $this->output->info('Adding virtual column virtual_contract_type in su_contracts...');
        $userDbConnectionCmd = $this->userDbConnection->prepare('ALTER TABLE su_contracts ADD COLUMN IF NOT EXISTS virtual_contract_type VARCHAR GENERATED ALWAYS AS (get_contract_type_by_id(nm_usage_rights)) STORED;');
        $userDbConnectionCmd->execute();

        $this->output->info('Deleting: ' . $fileName . '...');
        system('rm ' . $fileName);
        system('rm schema_' . $fileName);
    }
}
