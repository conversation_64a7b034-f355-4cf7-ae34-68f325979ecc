<?php

// GENERAL
define('SITE_TITLE', filter_var(getenv('LEGACY_MODE'), FILTER_VALIDATE_BOOLEAN) ? 'Technofarm' : 'Agrimi');
define('SITE_URL', getenv('SITE_URL') ? getenv('SITE_URL') : getSiteURL());
define('SITE_PATH', getenv('SITE_PATH'));
define('SITE_BASE_HREF', getenv('SITE_BASE_HREF') ?? '/');
define('MACHINE_NAME', getenv('MACHINE_NAME'));
define('APP_NAME', 'TechnoFarm');
define('APP_UNIQUE_KEY', getenv('APP_UNIQUE_KEY'));
define('MAX_UPLOAD_FILE_SIZE', 524288000); // Maximum file upload size in bytes
define('TEMPLATE_CACHE_PREFIX', 'prado:template:dev:');
define('WMS_SRS', 'EPSG:4326 EPSG:900913 EPSG:3857');
define('APP_SUPPORT_YEAR', '2019'); // Have to be removed
define('USER_SESSION_EXPIRATION_TIME', getenv('USER_SESSION_EXPIRATION_TIME') ?: 0);
define('SECURED_COOKIES', getenv('SECURED_COOKIES') ? getenv('SECURED_COOKIES') : false);
define('GEOSCAN_APP_URL', getenv('GEOSCAN_APP_URL'));
define('MAIN_NAVIGATION_INSTANCE', getenv('MAIN_NAVIGATION_INSTANCE'));
// END GENERAL

// DATEBASES CONFIGS
define('REMOTE_DB_MODULE', 'database2');
define('DEFAUL_DB_CRS', 32635);

define('DEFAULT_DB_DRIVER', 'pgsql');
define('DEFAULT_DB_DATABASE', getenv('DEFAULT_DB_DATABASE'));
define('DEFAULT_DB_PERSISTENT', 'false');
define('DEFAULT_DB_PREFIX', 'su_');
define('DEFAULT_DB_MODULE', 'database');
define('DEFAULT_DB_SOCKET', '');
define('DEFAULT_DB_VERSION', 9.3);
define('DEFAULT_DB_HOST', getenv('DEFAULT_DB_HOST'));
define('DEFAULT_DB_PORT', getenv('DEFAULT_DB_PORT'));
define('DEFAULT_DB_USERNAME', getenv('DEFAULT_DB_USERNAME'));
define('DEFAULT_DB_PASSWORD', getenv('DEFAULT_DB_PASSWORD'));

define('DBLINK_PERSISTENT', false);
define('DBLINK_SOCKET', '');
define('DBLINK_VERSION', 9.3);
define('DBLINK_MODULE', 'database');
define('DBLINK_DRIVER', 'pgsql');
define('DBLINK_HOST', getenv('DBLINK_HOST'));
define('DBLINK_PORT', getenv('DBLINK_PORT'));
define('DBLINK_USERNAME', getenv('DBLINK_USERNAME'));
define('DBLINK_PASSWORD', getenv('DBLINK_PASSWORD'));
define('DBLINK_DATABASE', getenv('DBLINK_DATABASE'));
// END DATEBASES CONFIGS

// WMS, MAP SERVER AND OPEN LAYER CONFIGS
define('GOOGLE_VERSION', 3.54);
define('GOOGLE_KEY', 'AIzaSyCP7hLKtuQLswTWA8THszoC16ph8so8jbw');

define('LOGIN3_WMS_SERVER', getenv('LOGIN3_WMS_SERVER'));
define('LOGIN3_WMS_MAP_PATH', getenv('LOGIN3_WMS_MAP_PATH'));

define('IMAGES_WMS_SERVER', 'http://images.technofarm.bg/mapcache'); // Unused
define('IMAGES_WMS_MAP_PATH', '/var/www/satellite_processor/maps/'); // Unused

define('OPEN_LAYERS_PROXY', getenv('OPEN_LAYERS_PROXY'));
define('WMS_SERVER', getenv('WMS_SERVER'));
// WMS_SERVER_INTERNAL is used when calling Mapserver from php from Docker
define('WMS_SERVER_INTERNAL', getenv('WMS_SERVER_INTERNAL') ? getenv('WMS_SERVER_INTERNAL') : getenv('WMS_SERVER'));
define('MAPSERVER_COMMANDS_PATH', getenv('MAPSERVER_COMMANDS_PATH'));
// END WMS, MAP SERVER AND OPEN LAYER CONFIGS

// PATHS CONFIGS
define('FRAMEWORK_PATH', 'vendor/pradosoft/prado/framework/Prado.php');

define('LOG_PATH', getenv('LOG_PATH'));
define('TEMPLATE_PATH', getenv('TEMPLATE_PATH'));
define('DEBUG_LOG_PATH', getenv('DEBUG_LOG_PATH'));
define('WMS_MAP_PATH', getenv('WMS_MAP_PATH'));

define('WIALON_PATH', 'https://login.farmtrack.bg/wialon/');
define('WIALON_SERVER', 'https://login.farmtrack.bg');

define('CSS_CUSTOM_THEME_NAME', getenv('CSS_CUSTOM_THEME_NAME') ? getenv('CSS_CUSTOM_THEME_NAME') : '');
define('PUBLIC_UPLOAD', SITE_PATH . 'public/files/uploads');
define('PUBLIC_UPLOAD_EXTRACT', SITE_PATH . 'public/files/uploads/extracts');
define('PUBLIC_PATH', SITE_PATH . 'public/');
define('PUBLIC_UPLOAD_RELATIVE_PATH', 'files/uploads');
define('PUBLIC_UPLOAD_EXPORT_RELATIVE_PATH', 'files/uploads/export/');
define('PUBLIC_UPLOAD_BLANKS_RELATIVE_PATH', 'files/uploads/blanks/');
define('PUBLIC_CONTRACTS_RELATIVE_PATH', 'files/contract_files');
define('PUBLIC_COOPERATORS_DOCUMENTS_PATH', 'files/cooperators_documents_files/');
define('PUBLIC_CONTRACTS_BLANKS_RELATIVE_PATH', 'files/contract_files/blanks/');
define('PUBLIC_SALES_CONTRACTS_RELATIVE_PATH', 'files/sales_contract_files/');
define('PUBLIC_UPLOAD_HISTORY_RELATIVE_PATH', 'files/uploads/history');
define('PUBLIC_UPLOAD_COVARAGE', PUBLIC_UPLOAD . '/coverage');
define('PUBLIC_UPLOAD_OVERLAPS', PUBLIC_UPLOAD . '/overlaps');
define('PUBLIC_UPLOAD_HISTORY', PUBLIC_UPLOAD . '/history');
define('PUBLIC_UPLOAD_COOPERATOR', PUBLIC_UPLOAD . '/cooperator');
define('PUBLIC_UPLOAD_BLANK', PUBLIC_UPLOAD . '/blanks');
define('PUBLIC_UPLOAD_EXPORT', PUBLIC_UPLOAD . '/export');
define('PUBLIC_UPLOAD_PLOT_DATA', PUBLIC_UPLOAD . '/plot_data_files');
define('WMS_IMAGE_PATH', PUBLIC_PATH . 'files/img_plots/');
define('WMS_IMAGE_URL', SITE_URL . 'files/img_plots/');
define('CSV2XLS_PATH', getenv('CSV2XLS_PATH') ? getenv('CSV2XLS_PATH') : '/var/www/.local/bin/');
define('OGR2OGR_PATH', getenv('OGR2OGR_PATH') ? getenv('OGR2OGR_PATH') : '/usr/bin/ogr2ogr');
define('SHP2PGSQL_PATH', getenv('SHP2PGSQL_PATH') ? getenv('SHP2PGSQL_PATH') : 'shp2pgsql');
define('PGSQL2SHP_PATH', getenv('PGSQL2SHP_PATH') ? getenv('PGSQL2SHP_PATH') : '/usr/bin/pgsql2shp');

define('JD_EXPORTER', getenv('JD_EXPORTER'));
define('JD_EXPORT_PATH', getenv('JD_EXPORT_PATH'));
define('AB_LINES_EXPORT_PATH', getenv('AB_LINES_EXPORT_PATH'));
define('AB_LINES_EXPORTER', getenv('AB_LINES_EXPORTER'));
define('COMMON_SERVICES_API_URL', getenv('COMMON_SERVICES_API_URL'));
define('SMART_CONVERTER', getenv('SMART_CONVERTER'));
define('WKHTMLTOPDF_PATH', getenv('WKHTMLTOPDF_PATH'));
define('SLOPE_FILE', getenv('SLOPE_FILE'));
// END PATHS CONFIGS

// MESSAGES CONSTANTS
define('LOADING_FILE', 0);
define('SUCCESSFULLY_TREATED', 1);
define('ERROR_INVALID_SHAPE', 2);
define('ERROR_INVALID_DBF', 3);
define('ERROR_INVALID_ARCHIVE', 4);
define('ERROR_INVALID_GEOMETRY', 5);
define('ERROR_INVALID_ISAK_FILE', 6);
define('ERROR_RUNTIME', 7);
define('ERROR_INVALID_TABLE_STRUCTURE', 8);
define('ERROR_INVALID_FILE_DATA', 9);
define('ERROR_WAITING_DEFINITION', 10);
define('ERROR_WAITING_COPYING', 11);
define('ERROR_INVALID_CRS', 12);
define('ERROR_NOT_ALLOWED_ADDING', 13);
define('ERROR_INCORRECT_ENCODING', 14);
define('ERROR_MISSING_COLUMN', 15);
define('ERROR_INCORRECT_ENCODING_FIELD', 16);
define('PARTIALLY_PROCESSED', 17);
define('INCONSISTENT_FILE_TYPE', 18);
define('LOADING_FILE_NOW', 19);
define('ERROR_READING_SHAPE_OBJECT', 20);
define('ERROR_GEOMETRY_COLLECTION', 21);
define('NOT_UPDATED_CONTRACTS', 22);
define('ERROR_INVALID_INPUT_SYNTAX', 23);
define('ERROR_SHP2PSQL_NOT_FOUND', 24);
define('ERROR_MISSING_KVS_EKATTE', 25);

// END MESSAGES CONSTANTS

// RPI CONFIGS
define('RPI_SERVER', getenv('RPI_SERVER'));
define('RPI_TOKEN', '');
define('RPI_USERNAME', getenv('RPI_USERNAME'));
define('RPI_PASSWORD', getenv('RPI_PASSWORD'));
// END RPI CONFIGS

// CRON SCRIPTS PATH
define('AGREEMENTS_QUEUE_PATH', SITE_PATH . 'public/files/layers_queue/');
define('COVERAGE_QUEUE_PATH', SITE_PATH . 'public/files/coverage_files/');
define('OVERLAPS_QUEUE_PATH', SITE_PATH . 'public/files/layers_queue/');
define('LAYERS_QUEUE_PATH', SITE_PATH . 'public/files/layers_queue/');
define('LAYERS_CONTRACTS_PATH', SITE_PATH . 'public/files/contract_files/');
define('SALES_CONTRACTS_PATH', SITE_PATH . 'public/files/sales_contract_files/');
define('SOIL_SAMPLES_QUEUE_PATH', SITE_PATH . 'public/files/soil_samples/');
define('COOPERATORS_DOCUMENTS_PATH', SITE_PATH . 'public/files/cooperators_documents_files/');
define('HYPOTHECS_FILES_PATH', SITE_PATH . 'public/files/hypothecs_files/');
define('OWNER_DOCUMENT_FILES', SITE_PATH . 'public/files/owners_documents_files/');
define('OWNER_FILES', SITE_PATH . 'public/files/owners_files/');
define('PAYROLL_EXPORTS_PATH', SITE_PATH . 'public/files/payrolls/');
define('CSD_FILES', SITE_PATH . 'public/files/csd_files/');
// END CRON SCRIPTS PATH

// WAREHOUSE CONFIGS
define('FARMS_TYPE_ID', 1);
define('CONTRAGENTS_TYPE_ID', 2);
define('MACHINES_TYPE_ID', 3);
define('WAREHOUSE_API_URL', getenv('WAREHOUSE_API_URL') ? getenv('WAREHOUSE_API_URL') : '');
// END WAREHOUSE CONFIGS

// MAIL CONFIGS
define('PHPMAILER_HOST', getenv('PHPMAILER_HOST') ? getenv('PHPMAILER_HOST') : '');
define('PHPMAILER_PORT', getenv('PHPMAILER_PORT') ? getenv('PHPMAILER_PORT') : '');
define('PHPMAILER_USERNAME', getenv('PHPMAILER_USERNAME') ? getenv('PHPMAILER_USERNAME') : '');
define('PHPMAILER_PASSWORD', getenv('PHPMAILER_PASSWORD') ? getenv('PHPMAILER_PASSWORD') : '');
define('CONTACT_FORM_FROM_NAME', 'Контактна форма');
define('CONTACT_FORM_TO_MAIL', '<EMAIL>');
define('CONTACT_FORM_SUBJECT', 'Контактна форма на Техно Фарм');
define('ALARMS_MAIL', getenv('ALARMS_MAIL') ? getenv('ALARMS_MAIL') : '');
// END MAIL CONFIGS
define('DFZ_REQUEST_URL', 'https://seu.dfz.bg/seu/f?p=727:8500:::NO:::#');
define('KAIS_REQUEST_URL', 'https://kais.cadastre.bg/bg/Map');

// Copy User Account Command params
define('COPY_USER_SOURCE_DB_HOST', getenv('COPY_USER_SOURCE_DB_HOST') ? getenv('COPY_USER_SOURCE_DB_HOST') : '');
define('COPY_USER_SOURCE_DB_PORT', getenv('COPY_USER_SOURCE_DB_PORT') ? getenv('COPY_USER_SOURCE_DB_PORT') : '');
define('COPY_USER_SOURCE_DB_USER', getenv('COPY_USER_SOURCE_DB_USER') ? getenv('COPY_USER_SOURCE_DB_USER') : '');
define('COPY_USER_SOURCE_DB_PASS', getenv('COPY_USER_SOURCE_DB_PASS') ? getenv('COPY_USER_SOURCE_DB_PASS') : '');
define('COPY_USER_COMMAND_SSH_HOST', getenv('COPY_USER_COMMAND_SSH_HOST') ? getenv('COPY_USER_COMMAND_SSH_HOST') : '');
define('COPY_USER_COMMAND_SSH_USER', getenv('COPY_USER_COMMAND_SSH_USER') ? getenv('COPY_USER_COMMAND_SSH_USER') : '');

// CMS API ENDPOINT
define('CMS_API_URL', getenv('CMS_API_URL'));
define('GEOSCAN_CMS_BASE_URI', getenv('GEOSCAN_CMS_BASE_URI'));
define('KAIS_SESSION_ID', getenv('KAIS_SESSION_ID'));
define('KAIS_CA_TOKEN', getenv('KAIS_CA_TOKEN'));

// INTEGRATIONS
define('HELPHERO_ID', getenv('HELPHERO_ID'));
define('POSTHOG_API_KEY', getenv('POSTHOG_API_KEY') ? getenv('POSTHOG_API_KEY') : '');
// INTEGRATIONS END

define('LEGACY_MODE', getenv('LEGACY_MODE') ? getenv('LEGACY_MODE') : false);

define('KVS_STORE_URL', getenv('KVS_STORE_URL'));
define('KVS_STORE_CALLBACK_URL', getenv('KVS_STORE_CALLBACK_URL'));

define('MAX_KVS_PROCESSING_FILES', getenv('MAX_KVS_PROCESSING_FILES') ? getenv('MAX_KVS_PROCESSING_FILES') : 10);
