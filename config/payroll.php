<?php

declare(strict_types=1);

return [
    /*
    |--------------------------------------------------------------------------
    | Payroll Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration settings for the agricultural payroll system including
    | precision settings, calculation parameters, and business rules.
    |
    */

    'precision' => [
        'area' => 3,
        'money' => 2,
        'rent_natura' => 3,
        'infinity' => 16,
    ],

    'rounding' => [
        'fix_money_rounding_value' => 0.1,
    ],

    'transaction_types' => [
        'payment' => 1,
    ],

    'rent_units' => [
        // This will be populated from database or can be defined here
        // Format: unit_id => ['name' => 'Unit Name', 'renta_nat_name' => 'Display Name']
    ],

    'farming_year' => [
        'start_month' => 10, // October
        'start_day' => 1,
    ],

    'personal_use' => [
        'inherit_from_parent' => true,
        'distribute_to_heirs' => true,
    ],

    'owner_tree' => [
        'max_depth' => 10,
        'path_separator' => '.',
    ],

    'payment_processing' => [
        'allow_overpayment' => true,
        'calculate_interest' => false,
    ],

    'display' => [
        'currency_symbol' => 'лв.',
        'date_format' => 'd.m.Y',
        'decimal_separator' => ',',
        'thousands_separator' => ' ',
    ],
];
