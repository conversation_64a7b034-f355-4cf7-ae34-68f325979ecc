# Owner Payroll System

A comprehensive Laravel-based system for managing agricultural rent calculations and owner payroll data.

## Quick Start

### API Endpoints

```bash
# Get payroll data for 2024
GET /api/payroll/owner-payroll?year=2024

# Get data for specific owner
GET /api/payroll/owner-payroll?year=2024&owner_id=123

# Get summary statistics
GET /api/payroll/summary?year=2024

# Export data
GET /api/payroll/export?year=2024&format=json
```

### Basic Usage

```php
use App\Actions\Payments\GetOwnerPayrollAction;

$payrollAction = new GetOwnerPayrollAction();

// Get payroll data
$result = $payrollAction->execute(2024);

// Get raw data array
$data = $payrollAction->getData(2024, $ownerId = 123);
```

## Features

- ✅ **High-Precision Calculations** - Uses bcmath for accurate financial calculations
- ✅ **Agricultural Year Logic** - Handles farming year calculations (Oct 1 - Sep 30)
- ✅ **Complex Owner Hierarchies** - Supports inheritance and path-based ownership
- ✅ **Natura Rent Processing** - Handles both monetary and in-kind rent payments
- ✅ **Personal Use Deductions** - Calculates personal use area deductions
- ✅ **Multi-Currency Support** - BGN to EUR conversion
- ✅ **Comprehensive Filtering** - Multiple filter options for data retrieval
- ✅ **Export Ready** - Formatted data for CSV, Excel, PDF export
- ✅ **Full Test Coverage** - Unit and feature tests included

## Architecture

### Action Pattern
Business logic is organized into focused Action classes:

- `GetOwnerPayrollAction` - Main orchestrator
- `GetPaymentsPlotsAction` - Plot data retrieval
- `ProcessOwnerPersonalUseAction` - Personal use calculations
- `AggregatePaymentPlotsAction` - Data aggregation
- `ContractsPaymentsMappingAction` - Payment mapping
- `FormattingOwnersDataAction` - Display formatting

### Utilities
- `Math` - High-precision mathematical operations
- `PayrollHelper` - Agricultural-specific helper functions

## Configuration

Key settings in `config/payroll.php`:

```php
'precision' => [
    'area' => 3,        // Decimal places for area (дка)
    'money' => 2,       // Decimal places for money (лв.)
    'rent_natura' => 3, // Decimal places for natura rent
],
```

## API Reference

### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `year` | integer | Farming year (required) |
| `owner_id` | integer | Specific owner ID |
| `path` | string | Owner path for hierarchical data |
| `filter_params` | array | Additional filters |

### Filter Parameters

```php
'filter_params' => [
    'payroll_farming' => [1, 2, 3],     // Farming IDs
    'owner_names' => 'John Doe',         // Owner name search
    'egn' => '1234567890',               // EGN filter
    'contract_id' => 123,                // Contract ID
    'ekatte' => '12345',                 // EKATTE code
    'kad_ident' => 'KAD123',             // Cadastral identifier
    'no_contract_payments' => true,       // Exclude contract payments
]
```

### Response Structure

```json
{
    "success": true,
    "data": [
        {
            "owner": {
                "id": 123,
                "names": "John Doe",
                "egn_eik": "1234567890"
            },
            "areas": {
                "owner_area": {
                    "formatted": "10,500 дка",
                    "raw": 10.5
                }
            },
            "rent": {
                "total_rent": {
                    "formatted": "1 000,00 лв.",
                    "raw": 1000.0,
                    "eur": "511,29 €"
                },
                "paid_rent": {
                    "formatted": "500,00 лв.",
                    "raw": 500.0
                },
                "unpaid_rent": {
                    "formatted": "500,00 лв.",
                    "raw": 500.0
                }
            },
            "payment_status": "partially_paid"
        }
    ],
    "meta": {
        "year": 2024,
        "owner_id": 123,
        "total_records": 1
    }
}
```

## Testing

```bash
# Run all payroll tests
php artisan test --filter=Payroll

# Run specific test suites
php artisan test tests/Feature/Payments/
php artisan test tests/Unit/Utils/
php artisan test tests/Unit/Actions/Payments/
```

## Examples

### Get All Owners for Year
```php
$data = $payrollAction->getData(2024);
```

### Get Specific Owner with Path
```php
$data = $payrollAction->getData(2024, 123, '1.2.3');
```

### Filter by Owner Names
```php
$data = $payrollAction->getData(2024, null, null, [
    'owner_names' => 'Иван Петров'
]);
```

### Get Summary Statistics
```bash
curl "http://localhost/api/payroll/summary?year=2024"
```

### Export Data
```bash
curl "http://localhost/api/payroll/export?year=2024&format=json"
```

## Performance

### Database Optimization
- Proper indexing on frequently queried columns
- Query optimization using Laravel Query Builder
- Pagination support for large datasets

### Memory Management
- Efficient data processing using Laravel Collections
- Configurable precision settings to balance accuracy vs performance

### Caching
- Built-in Laravel caching support
- Configurable cache TTL for different data types

## Migration from Legacy

This system replaces the legacy `getOwnerPayroll()` method with:

1. **Better Architecture** - Separation of concerns using Action pattern
2. **Modern Laravel** - Query Builder, Resources, Validation
3. **Type Safety** - Strict typing throughout
4. **Testability** - Comprehensive test coverage
5. **Maintainability** - Clear documentation and structure

## Troubleshooting

### Common Issues

**Precision Errors**
```bash
# Ensure bcmath extension is installed
php -m | grep bcmath
```

**Memory Issues**
```php
// Use pagination for large datasets
$data = $payrollAction->getData(2024, null, null, [
    'page' => 1,
    'per_page' => 100
]);
```

**Performance Issues**
```sql
-- Check for missing indexes
EXPLAIN SELECT * FROM su_payments WHERE farming_year = 2024;
```

### Debug Mode
```php
// Enable query logging
DB::enableQueryLog();
$data = $payrollAction->getData(2024);
dd(DB::getQueryLog());
```

## Support

For issues and questions:
1. Check the comprehensive documentation in `docs/payroll-migration.md`
2. Review test cases for usage examples
3. Enable Laravel debugging for detailed error information

## License

This payroll system is part of the TechnoFarm Laravel application.
