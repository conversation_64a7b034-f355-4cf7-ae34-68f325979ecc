<?php

declare(strict_types=1);

namespace App\Utils;

use Carbon\Carbon;

/**
 * Helper utilities for payroll calculations
 * 
 * This class provides utility functions for common payroll operations
 * including date calculations, currency conversions, and data formatting.
 */
class PayrollHelper
{
    /**
     * Get farming year from date
     * 
     * Farming year starts on October 1st and ends on September 30th
     */
    public static function getFarmingYearFromDate(string|Carbon $date): array
    {
        $carbon = $date instanceof Carbon ? $date : Carbon::parse($date);
        
        $startMonth = config('payroll.farming_year.start_month', 10);
        $startDay = config('payroll.farming_year.start_day', 1);
        
        // If date is before October 1st, farming year is previous calendar year
        if ($carbon->month < $startMonth || ($carbon->month === $startMonth && $carbon->day < $startDay)) {
            $farmingYear = $carbon->year - 1;
        } else {
            $farmingYear = $carbon->year;
        }
        
        return [
            'id' => $farmingYear,
            'name' => $farmingYear . '/' . ($farmingYear + 1),
            'start_date' => Carbon::create($farmingYear, $startMonth, $startDay),
            'end_date' => Carbon::create($farmingYear + 1, $startMonth, $startDay)->subDay(),
        ];
    }

    /**
     * Convert BGN to EUR (placeholder - implement actual conversion)
     */
    public static function bgnToEuro(float $amount, ?float $rate = null): float
    {
        // Default EUR/BGN rate (Bulgarian Lev is pegged to Euro at ~1.95583)
        $rate = $rate ?? 1.95583;
        
        return Math::div($amount, $rate, config('payroll.precision.money', 2));
    }

    /**
     * Format currency amount for display
     */
    public static function formatCurrency(float $amount, string $currency = 'BGN'): string
    {
        $precision = config('payroll.precision.money', 2);
        $decimalSeparator = config('payroll.display.decimal_separator', ',');
        $thousandsSeparator = config('payroll.display.thousands_separator', ' ');
        $symbol = config('payroll.display.currency_symbol', 'лв.');
        
        $formatted = number_format($amount, $precision, $decimalSeparator, $thousandsSeparator);
        
        return $formatted . ' ' . $symbol;
    }

    /**
     * Format area for display
     */
    public static function formatArea(float $area): string
    {
        $precision = config('payroll.precision.area', 3);
        $decimalSeparator = config('payroll.display.decimal_separator', ',');
        $thousandsSeparator = config('payroll.display.thousands_separator', ' ');
        
        return number_format($area, $precision, $decimalSeparator, $thousandsSeparator) . ' дка';
    }

    /**
     * Generate owner path key
     */
    public static function generateOwnerPathKey(int $ownerId, ?string $path = null): string
    {
        return $ownerId . '_' . ($path ?? '0');
    }

    /**
     * Parse owner path key
     */
    public static function parseOwnerPathKey(string $pathKey): array
    {
        $parts = explode('_', $pathKey, 2);
        
        return [
            'owner_id' => (int) $parts[0],
            'path' => $parts[1] ?? '0',
        ];
    }

    /**
     * Check if owner is dead in current farming year
     */
    public static function isDeadInCurrentFarmingYear(?string $deadDate, int $farmingYear): bool
    {
        if (!$deadDate) {
            return false;
        }
        
        $deadFarmingYear = self::getFarmingYearFromDate($deadDate);
        
        return $deadFarmingYear['id'] === $farmingYear;
    }

    /**
     * Generate unique identifier for aggregation
     */
    public static function generateUuid(string $input): string
    {
        return substr(md5($input), 0, 6);
    }

    /**
     * Build hierarchical path from parent path and current ID
     */
    public static function buildPath(?string $parentPath, int $currentId): string
    {
        if (!$parentPath || $parentPath === '0') {
            return (string) $currentId;
        }
        
        $separator = config('payroll.owner_tree.path_separator', '.');
        
        return $parentPath . $separator . $currentId;
    }

    /**
     * Parse hierarchical path into array of IDs
     */
    public static function parsePath(string $path): array
    {
        if (!$path || $path === '0') {
            return [];
        }
        
        $separator = config('payroll.owner_tree.path_separator', '.');
        
        return array_map('intval', explode($separator, $path));
    }

    /**
     * Check if path contains specific owner ID
     */
    public static function pathContainsOwner(string $path, int $ownerId): bool
    {
        $pathIds = self::parsePath($path);
        
        return in_array($ownerId, $pathIds, true);
    }

    /**
     * Get depth of hierarchical path
     */
    public static function getPathDepth(string $path): int
    {
        if (!$path || $path === '0') {
            return 0;
        }
        
        return count(self::parsePath($path));
    }

    /**
     * Validate precision value
     */
    public static function validatePrecision(int $precision): int
    {
        return max(0, min($precision, config('payroll.precision.infinity', 16)));
    }
}
