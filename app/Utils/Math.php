<?php

declare(strict_types=1);

namespace App\Utils;

use InvalidArgumentException;

/**
 * Mathematical utility class for precision calculations
 * 
 * This class provides high-precision mathematical operations for financial
 * and area calculations in the agricultural rent management system.
 * All operations maintain precision to avoid floating-point errors.
 */
class Math
{
    /**
     * Add two numbers with specified precision
     */
    public static function add(float|int|string|null $a, float|int|string|null $b, int $precision = 2): float
    {
        $a = self::normalizeValue($a);
        $b = self::normalizeValue($b);
        
        $result = bcadd((string) $a, (string) $b, $precision + 2);
        return (float) bcadd($result, '0', $precision);
    }

    /**
     * Subtract two numbers with specified precision
     */
    public static function sub(float|int|string|null $a, float|int|string|null $b, int $precision = 2): float
    {
        $a = self::normalizeValue($a);
        $b = self::normalizeValue($b);
        
        $result = bcsub((string) $a, (string) $b, $precision + 2);
        return (float) bcadd($result, '0', $precision);
    }

    /**
     * Multiply two numbers with specified precision
     */
    public static function mul(float|int|string|null $a, float|int|string|null $b, int $precision = 2): float
    {
        $a = self::normalizeValue($a);
        $b = self::normalizeValue($b);
        
        $result = bcmul((string) $a, (string) $b, $precision + 2);
        return (float) bcadd($result, '0', $precision);
    }

    /**
     * Divide two numbers with specified precision
     */
    public static function div(float|int|string|null $a, float|int|string|null $b, int $precision = 2): float
    {
        $a = self::normalizeValue($a);
        $b = self::normalizeValue($b);
        
        if (bccomp((string) $b, '0', 10) === 0) {
            throw new InvalidArgumentException('Division by zero');
        }
        
        $result = bcdiv((string) $a, (string) $b, $precision + 2);
        return (float) bcadd($result, '0', $precision);
    }

    /**
     * Round a number to specified precision
     */
    public static function round(float|int|string|null $value, int $precision = 2): float
    {
        $value = self::normalizeValue($value);
        
        $multiplier = bcpow('10', (string) $precision, 0);
        $multiplied = bcmul((string) $value, $multiplier, $precision + 2);
        
        // Add 0.5 for positive numbers, subtract 0.5 for negative numbers
        if (bccomp($multiplied, '0', $precision + 2) >= 0) {
            $rounded = bcadd($multiplied, '0.5', $precision + 2);
        } else {
            $rounded = bcsub($multiplied, '0.5', $precision + 2);
        }
        
        // Truncate to integer and divide back
        $truncated = bcdiv($rounded, '1', 0);
        $result = bcdiv($truncated, $multiplier, $precision);
        
        return (float) $result;
    }

    /**
     * Compare two numbers
     * Returns: -1 if a < b, 0 if a == b, 1 if a > b
     */
    public static function compare(float|int|string|null $a, float|int|string|null $b, int $precision = 2): int
    {
        $a = self::normalizeValue($a);
        $b = self::normalizeValue($b);
        
        return bccomp((string) $a, (string) $b, $precision);
    }

    /**
     * Check if two numbers are equal within precision
     */
    public static function equals(float|int|string|null $a, float|int|string|null $b, int $precision = 2): bool
    {
        return self::compare($a, $b, $precision) === 0;
    }

    /**
     * Get absolute value of a number
     */
    public static function abs(float|int|string|null $value, int $precision = 2): float
    {
        $value = self::normalizeValue($value);
        
        if (bccomp((string) $value, '0', $precision) < 0) {
            return self::mul($value, -1, $precision);
        }
        
        return (float) bcadd((string) $value, '0', $precision);
    }

    /**
     * Get maximum of two numbers
     */
    public static function max(float|int|string|null $a, float|int|string|null $b, int $precision = 2): float
    {
        return self::compare($a, $b, $precision) >= 0 ? self::normalizeValue($a) : self::normalizeValue($b);
    }

    /**
     * Get minimum of two numbers
     */
    public static function min(float|int|string|null $a, float|int|string|null $b, int $precision = 2): float
    {
        return self::compare($a, $b, $precision) <= 0 ? self::normalizeValue($a) : self::normalizeValue($b);
    }

    /**
     * Normalize input value to float
     */
    private static function normalizeValue(float|int|string|null $value): float
    {
        if ($value === null) {
            return 0.0;
        }
        
        if (is_string($value)) {
            $value = trim($value);
            if ($value === '' || $value === '-') {
                return 0.0;
            }
        }
        
        return (float) $value;
    }

    /**
     * Sum an array of values with precision
     */
    public static function sum(array $values, int $precision = 2): float
    {
        $result = 0.0;
        
        foreach ($values as $value) {
            $result = self::add($result, $value, $precision);
        }
        
        return $result;
    }

    /**
     * Calculate percentage of a value
     */
    public static function percentage(float|int|string|null $value, float|int|string|null $percentage, int $precision = 2): float
    {
        $decimal = self::div($percentage, 100, $precision + 2);
        return self::mul($value, $decimal, $precision);
    }
}
