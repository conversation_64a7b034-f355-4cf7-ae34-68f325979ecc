<?php

declare(strict_types=1);

namespace App\Models\UserDb\Plots;

use App\Models\UserDb\Documents\Contracts\ContractPlot;
use App\Models\UserDb\Documents\Contracts\PlotOwner;
use App\Models\UserDb\Owners\Owner;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Plot Model
 *
 * Represents agricultural plots/parcels in the system.
 * Each plot can have multiple owners and be part of multiple contracts.
 *
 * @property int $id
 * @property string $kad_ident Cadastral identifier
 * @property string|null $ekatte_name Settlement name
 * @property string|null $mestnost Locality/area name
 * @property string|null $category Land category
 * @property float $area Total area of the plot
 * @property int|null $area_type_id Foreign key to area types
 * @property string|null $way_of_use Way of land use
 * @property string|null $remark Additional remarks
 */
class Plot extends Model
{
    protected $table = 'su_plots';

    protected $fillable = [
        'kad_ident',
        'ekatte_name',
        'mestnost',
        'category',
        'area',
        'area_type_id',
        'way_of_use',
        'remark',
    ];

    protected $casts = [
        'area' => 'decimal:4',
    ];

    /**
     * Get the area type for this plot
     */
    public function areaType(): BelongsTo
    {
        return $this->belongsTo(AreaType::class, 'area_type_id');
    }

    /**
     * Get the plot owners for this plot
     */
    public function plotOwners(): HasMany
    {
        return $this->hasMany(PlotOwner::class, 'plot_id');
    }

    /**
     * Get the contract plots for this plot
     */
    public function contractPlots(): HasMany
    {
        return $this->hasMany(ContractPlot::class, 'plot_id');
    }

    /**
     * Get all owners for this plot through plot owners
     */
    public function owners(): HasManyThrough
    {
        return $this->hasManyThrough(Owner::class, PlotOwner::class, 'plot_id', 'id', 'id', 'owner_id');
    }

    /**
     * Scope for plots with specific cadastral identifier
     */
    public function scopeByKadIdent($query, string $kadIdent)
    {
        return $query->where('kad_ident', $kadIdent);
    }

    /**
     * Scope for plots in specific settlement
     */
    public function scopeByEkatte($query, string $ekatte)
    {
        return $query->where('ekatte_name', $ekatte);
    }

    /**
     * Scope for plots with minimum area
     */
    public function scopeMinArea($query, float $minArea)
    {
        return $query->where('area', '>=', $minArea);
    }
}
