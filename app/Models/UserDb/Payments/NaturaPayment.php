<?php

declare(strict_types=1);

namespace App\Models\UserDb\Payments;

use App\Models\UserDb\Rent\RentInKind;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Natura Payment Model
 *
 * Represents payments made in natural products (crops, livestock, etc.)
 * instead of monetary payments.
 *
 * @property int $id
 * @property int $payment_id Foreign key to payments
 * @property int $nat_type Foreign key to rent in kind types
 * @property float $amount Amount of natural product
 * @property float|null $unit_value Value per unit
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 */
class NaturaPayment extends Model
{
    protected $table = 'su_natura_payments';

    protected $fillable = [
        'payment_id',
        'nat_type',
        'amount',
        'unit_value',
    ];

    protected $casts = [
        'amount' => 'decimal:4',
        'unit_value' => 'decimal:4',
    ];

    /**
     * Get the payment that owns this natura payment
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class, 'payment_id');
    }

    /**
     * Get the rent in kind type for this natura payment
     */
    public function rentInKind(): BelongsTo
    {
        return $this->belongsTo(RentInKind::class, 'nat_type');
    }

    /**
     * Calculate the monetary value of this natura payment
     */
    public function getMonetaryValueAttribute(): float
    {
        return $this->amount * ($this->unit_value ?? 0);
    }
}
