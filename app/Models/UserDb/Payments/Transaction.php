<?php

declare(strict_types=1);

namespace App\Models\UserDb\Payments;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Transaction Model
 *
 * Represents payment transactions in the system.
 * Each transaction can have multiple payments associated with it.
 *
 * @property int $id
 * @property int $type Transaction type (1 = payment, etc.)
 * @property string|null $status Transaction status
 * @property string|null $description Transaction description
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 */
class Transaction extends Model
{
    protected $table = 'su_transactions';

    protected $fillable = [
        'type',
        'status',
        'description',
    ];

    protected $casts = [
        'type' => 'integer',
    ];

    /**
     * Get the payments for this transaction
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'transaction_id');
    }

    /**
     * Scope for payment transactions
     */
    public function scopePayments($query)
    {
        return $query->where('type', config('payroll.transaction_types.payment', 1));
    }

    /**
     * Scope for active transactions
     */
    public function scopeActive($query)
    {
        return $query->whereNotNull('status');
    }
}
