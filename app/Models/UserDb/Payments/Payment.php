<?php

declare(strict_types=1);

namespace App\Models\UserDb\Payments;

use App\Models\UserDb\Documents\Contracts\Contract;
use App\Models\UserDb\Owners\Owner;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Payment Model
 *
 * Represents payments made by owners for agricultural rent.
 * Can include both monetary payments and payments in natural products.
 *
 * @property int $id
 * @property int $contract_id Foreign key to contracts
 * @property int|null $owner_id Foreign key to owners
 * @property int|null $transaction_id Foreign key to transactions
 * @property float $amount Payment amount
 * @property string|null $path Owner path for hierarchical structure
 * @property int $farming_year Farming year for this payment
 * @property \Carbon\Carbon $payment_date Date of payment
 * @property string|null $payment_type Type of payment
 * @property string|null $description Payment description
 * @property string|null $status Payment status
 */
class Payment extends Model
{
    protected $table = 'su_payments';

    protected $fillable = [
        'contract_id',
        'owner_id',
        'transaction_id',
        'amount',
        'path',
        'farming_year',
        'payment_date',
        'payment_type',
        'description',
        'status',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'farming_year' => 'integer',
        'payment_date' => 'date',
    ];

    /**
     * Get the contract that owns this payment
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    /**
     * Get the owner that made this payment
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(Owner::class, 'owner_id');
    }

    /**
     * Get the transaction for this payment
     */
    public function transaction(): BelongsTo
    {
        return $this->belongsTo(Transaction::class, 'transaction_id');
    }

    /**
     * Get the natura payments for this payment
     */
    public function naturaPayments(): HasMany
    {
        return $this->hasMany(NaturaPayment::class, 'payment_id');
    }

    /**
     * Scope for payments in specific farming year
     */
    public function scopeForYear($query, int $year)
    {
        return $query->where('farming_year', $year);
    }

    /**
     * Scope for payments by specific owner
     */
    public function scopeByOwner($query, int $ownerId)
    {
        return $query->where('owner_id', $ownerId);
    }

    /**
     * Scope for payments with specific path
     */
    public function scopeByPath($query, string $path)
    {
        return $query->where('path', $path);
    }
}
