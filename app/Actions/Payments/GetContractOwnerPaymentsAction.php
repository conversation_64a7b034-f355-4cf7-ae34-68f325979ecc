<?php

declare(strict_types=1);

namespace App\Actions\Payments;

use App\Utils\Math;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

/**
 * Get Contract Owner Payments Action
 *
 * Retrieves payment data for contract owners.
 * This is the Laravel migration of the legacy getContractOwnerPayments() method.
 */
class GetContractOwnerPaymentsAction
{
    private int $moneyPrecision;
    private int $rentaNatPrecision;

    public function __construct()
    {
        $this->moneyPrecision = config('payroll.precision.money', 2);
        $this->rentaNatPrecision = config('payroll.precision.rent_natura', 3);
    }

    /**
     * Execute contract owner payments retrieval
     * 
     * Maintains the original method signature and logic:
     * - Builds complex query with multiple joins
     * - Handles owner path logic
     * - Returns payment data with natura rent information
     */
    public function execute(
        int $year,
        ?array $contractAnnexIds = null,
        ?string $ownerIdPath = null,
        bool $returnCount = false
    ): Collection|int {
        // Build the main query using Laravel Query Builder
        $query = DB::table('su_payments as p')
            ->leftJoin('su_natura_payments as pn', 'p.id', '=', 'pn.payment_id')
            ->leftJoin('su_renta_types as rent', 'pn.nat_type', '=', 'rent.id')
            ->join('su_transactions as t', 't.id', '=', 'p.transaction_id')
            ->whereNotNull('t.status')
            ->where('t.type', config('payroll.transaction_types.payment', 1))
            ->where('p.farming_year', $year);

        // Apply contract filter
        if ($contractAnnexIds) {
            $query->whereIn('p.contract_id', $contractAnnexIds);
        }

        // Handle owner path logic
        if ($ownerIdPath) {
            $this->applyOwnerPathFilter($query, $ownerIdPath);
        }

        // Return count if requested
        if ($returnCount) {
            return $query->distinct()->count('p.owner_id');
        }

        // Select all required fields
        $query->select([
            'p.id as payment_id',
            'p.owner_id',
            'p.path as owner_path',
            DB::raw("CONCAT(p.owner_id, '_', COALESCE(CAST(p.path AS CHAR), '0')) as owner_path_key"),
            'p.contract_id',
            'p.amount as payment_amount',
            'p.farming_year',
            'p.payment_date',
            'p.payment_type',
            'p.status as payment_status',
            
            // Transaction amount calculation
            DB::raw('CASE WHEN pn.amount IS NOT NULL AND pn.unit_value IS NOT NULL 
                     THEN pn.amount * pn.unit_value ELSE p.amount END as trans_amount'),
            
            // Natura payment fields
            'pn.amount as natura_amount',
            'pn.unit_value as natura_unit_value',
            'pn.nat_type as natura_type_id',
            'rent.name as natura_type_name',
            'rent.unit as natura_unit_id',
            
            // Additional fields for calculations
            DB::raw('COALESCE(p.amount, 0) as paid_renta'),
            DB::raw('0 as unpaid_renta'),
            DB::raw('0 as overpaid_renta'),
            DB::raw('COALESCE(p.amount, 0) as paid_via_money'),
            DB::raw('COALESCE(pn.amount, 0) as paid_via_nat'),
        ]);

        // Order by contract for consistent results
        $query->orderBy('p.contract_id');

        return $query->get();
    }

    /**
     * Apply owner path filter to query
     */
    private function applyOwnerPathFilter($query, string $ownerIdPath): void
    {
        // Handle different owner path formats
        if (str_contains($ownerIdPath, '_')) {
            // Format: "owner_id_path"
            [$ownerId, $path] = explode('_', $ownerIdPath, 2);
            $query->where('p.owner_id', $ownerId)
                  ->where('p.path', $path);
        } else {
            // Simple owner ID
            $query->where('p.owner_id', $ownerIdPath);
        }
    }

    /**
     * Get paid data with complex options (legacy method compatibility)
     */
    public function getPaidData(array $options, bool $counter = false, bool $returnOnlySQL = false): Collection|int|string
    {
        $query = DB::table('su_payments as p')
            ->leftJoin('su_natura_payments as pn', 'p.id', '=', 'pn.payment_id');

        // Apply return fields
        if (isset($options['return']) && is_array($options['return'])) {
            $query->select($options['return']);
        } else {
            $query->select('p.*');
        }

        // Apply custom counter
        if (isset($options['custom_counter'])) {
            $query->selectRaw($options['custom_counter'] . ' as total_count');
        }

        // Apply where conditions
        if (isset($options['where']) && is_array($options['where'])) {
            foreach ($options['where'] as $condition) {
                $this->applyWhereCondition($query, $condition);
            }
        }

        // Apply ordering
        if (isset($options['sort']) && isset($options['order'])) {
            $query->orderBy($options['sort'], $options['order']);
        }

        // Return SQL string if requested
        if ($returnOnlySQL) {
            return $query->toSql();
        }

        // Return count if requested
        if ($counter) {
            return $query->count();
        }

        return $query->get();
    }

    /**
     * Apply where condition to query
     */
    private function applyWhereCondition($query, array $condition): void
    {
        $column = $condition['column'];
        $compare = $condition['compare'];
        $value = $condition['value'];
        $prefix = $condition['prefix'] ?? '';

        $fullColumn = $prefix ? $prefix . '.' . $column : $column;

        match ($compare) {
            '=' => $query->where($fullColumn, $value),
            '!=' => $query->where($fullColumn, '!=', $value),
            '>' => $query->where($fullColumn, '>', $value),
            '<' => $query->where($fullColumn, '<', $value),
            '>=' => $query->where($fullColumn, '>=', $value),
            '<=' => $query->where($fullColumn, '<=', $value),
            'IN' => $query->whereIn($fullColumn, is_array($value) ? $value : [$value]),
            'NOT IN' => $query->whereNotIn($fullColumn, is_array($value) ? $value : [$value]),
            'LIKE' => $query->where($fullColumn, 'LIKE', $value),
            'NOT LIKE' => $query->where($fullColumn, 'NOT LIKE', $value),
            default => $query->where($fullColumn, $value),
        };
    }

    /**
     * Process payment data for owner calculations
     */
    public function processPaymentData(Collection $payments): array
    {
        $processedData = [];

        foreach ($payments as $payment) {
            $ownerPathKey = $payment->owner_path_key;

            if (!isset($processedData[$ownerPathKey])) {
                $processedData[$ownerPathKey] = $this->initializeOwnerPaymentData($payment);
            }

            $this->aggregatePaymentData($processedData[$ownerPathKey], $payment);
        }

        return $processedData;
    }

    /**
     * Initialize owner payment data structure
     */
    private function initializeOwnerPaymentData(object $payment): array
    {
        return [
            'owner_id' => $payment->owner_id,
            'owner_path' => $payment->owner_path,
            'owner_path_key' => $payment->owner_path_key,
            'contract_id' => $payment->contract_id,
            'paid_renta' => 0.0,
            'unpaid_renta' => 0.0,
            'overpaid_renta' => 0.0,
            'paid_via_money' => 0.0,
            'paid_via_nat' => 0.0,
            'paid_nat_via_money' => 0.0,
            'paid_nat_via_nat' => [],
            'paid_renta_nat_sum' => [],
            'overpaid_renta_nat_money_arr' => [],
            'unpaid_renta_nat_money_arr' => [],
            'overpaid_renta_nat_arr' => [],
            'unpaid_renta_nat_arr' => [],
            'payments' => [],
        ];
    }

    /**
     * Aggregate payment data for owner
     */
    private function aggregatePaymentData(array &$ownerData, object $payment): void
    {
        // Add payment to list
        $ownerData['payments'][] = $payment;

        // Aggregate monetary amounts
        $ownerData['paid_renta'] = Math::add($ownerData['paid_renta'], $payment->paid_renta ?? 0);
        $ownerData['paid_via_money'] = Math::add($ownerData['paid_via_money'], $payment->paid_via_money ?? 0);

        // Aggregate natura payments
        if ($payment->natura_amount && $payment->natura_type_id) {
            $naturaTypeId = $payment->natura_type_id;
            
            if (!isset($ownerData['paid_nat_via_nat'][$naturaTypeId])) {
                $ownerData['paid_nat_via_nat'][$naturaTypeId] = 0.0;
            }
            
            $ownerData['paid_nat_via_nat'][$naturaTypeId] = Math::add(
                $ownerData['paid_nat_via_nat'][$naturaTypeId],
                $payment->natura_amount,
                $this->rentaNatPrecision
            );

            // Calculate monetary value of natura payment
            if ($payment->natura_unit_value) {
                $naturaMoneyValue = Math::mul($payment->natura_amount, $payment->natura_unit_value);
                $ownerData['paid_nat_via_money'] = Math::add($ownerData['paid_nat_via_money'], $naturaMoneyValue);
            }
        }
    }
}
