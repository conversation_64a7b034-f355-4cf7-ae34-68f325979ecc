<?php

declare(strict_types=1);

namespace App\Actions\Payments;

use App\Utils\Math;
use App\Utils\PayrollHelper;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

/**
 * Get Owner Payroll Action
 *
 * Main action class that orchestrates the owner payroll calculation process.
 * This is the Laravel migration of the legacy getOwnerPayroll() method.
 */
class GetOwnerPayrollAction
{
    private int $areaPrecision;
    private int $moneyPrecision;
    private int $rentaNatPrecision;
    private array $personalUse = [];

    public function __construct(
        private GetPaymentsPlotsAction $getPaymentsPlotsAction,
        private ProcessOwnerPersonalUseAction $processOwnerPersonalUseAction,
        private AggregatePaymentPlotsAction $aggregatePaymentPlotsAction,
        private ContractsPaymentsMappingAction $contractsPaymentsMappingAction,
        private FormattingOwnersDataAction $formattingOwnersDataAction
    ) {
        $this->areaPrecision = config('payroll.precision.area', 3);
        $this->moneyPrecision = config('payroll.precision.money', 2);
        $this->rentaNatPrecision = config('payroll.precision.rent_natura', 3);
    }

    /**
     * Execute the owner payroll calculation
     * 
     * This method maintains the original signature and logic flow:
     * 1. Get payments plots data
     * 2. Process owner personal use
     * 3. Aggregate plots by rent type and contract
     * 4. Map contract payments (if not disabled)
     * 5. Format data for display
     */
    public function execute(
        int $year,
        ?int $ownerId = null,
        ?string $path = null,
        array $filterParams = []
    ): JsonResponse {
        // Step 1: Get all plots for the specified criteria with rent data
        $plots = $this->getPaymentsPlotsAction->execute(
            $year,
            null, // contractId
            null, // annexId
            $ownerId,
            $path,
            $filterParams
        );

        // Step 2: Process personal use data
        $plots = $this->processOwnerPersonalUseAction->execute($plots);

        // Step 3: Aggregate plots by rent type
        $aggPlots = $this->aggregatePaymentPlotsAction->execute($plots, 'rent_type');
        
        // Step 4: Aggregate plots by contract
        $aggPlotsByContract = $this->aggregatePaymentPlotsAction->execute($plots, 'contract');

        // Step 5: Map contract payments and calculate remaining rents (unless disabled)
        if (!isset($filterParams['no_contract_payments'])) {
            $aggPlots = $this->contractsPaymentsMappingAction->execute(
                $aggPlots,
                $aggPlotsByContract,
                $year,
                $ownerId,
                $path
            );
        }

        // Step 6: Format data for display
        $formattedData = $this->formattingOwnersDataAction->execute($aggPlots);

        return response()->json([
            'success' => true,
            'data' => $formattedData,
            'meta' => [
                'year' => $year,
                'owner_id' => $ownerId,
                'path' => $path,
                'total_records' => count($formattedData),
            ],
        ]);
    }

    /**
     * Get owner payroll data without JSON response (for internal use)
     */
    public function getData(
        int $year,
        ?int $ownerId = null,
        ?string $path = null,
        array $filterParams = []
    ): array {
        // Same logic as execute() but returns array instead of JsonResponse
        $plots = $this->getPaymentsPlotsAction->execute(
            $year,
            null,
            null,
            $ownerId,
            $path,
            $filterParams
        );

        $plots = $this->processOwnerPersonalUseAction->execute($plots);
        $aggPlots = $this->aggregatePaymentPlotsAction->execute($plots, 'rent_type');
        $aggPlotsByContract = $this->aggregatePaymentPlotsAction->execute($plots, 'contract');

        if (!isset($filterParams['no_contract_payments'])) {
            $aggPlots = $this->contractsPaymentsMappingAction->execute(
                $aggPlots,
                $aggPlotsByContract,
                $year,
                $ownerId,
                $path
            );
        }

        return $this->formattingOwnersDataAction->execute($aggPlots);
    }

    /**
     * Validate input parameters
     */
    private function validateParameters(
        int $year,
        ?int $ownerId = null,
        ?string $path = null,
        array $filterParams = []
    ): void {
        if ($year < 2000 || $year > 2100) {
            throw new \InvalidArgumentException('Invalid farming year provided');
        }

        if ($ownerId !== null && $ownerId <= 0) {
            throw new \InvalidArgumentException('Invalid owner ID provided');
        }

        if ($path !== null && !is_string($path)) {
            throw new \InvalidArgumentException('Invalid path provided');
        }
    }

    /**
     * Get precision settings
     */
    public function getPrecisionSettings(): array
    {
        return [
            'area' => $this->areaPrecision,
            'money' => $this->moneyPrecision,
            'rent_natura' => $this->rentaNatPrecision,
        ];
    }
}
