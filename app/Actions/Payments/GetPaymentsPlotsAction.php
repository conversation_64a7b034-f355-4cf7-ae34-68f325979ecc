<?php

declare(strict_types=1);

namespace App\Actions\Payments;

use App\Utils\Math;
use App\Utils\PayrollHelper;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

/**
 * Get Payments Plots Action
 *
 * Retrieves plot data with rent information for payroll calculations.
 * This is the Laravel migration of the legacy getPaymentsPlots() method.
 */
class GetPaymentsPlotsAction
{
    private int $areaPrecision;
    private int $moneyPrecision;
    private int $rentaNatPrecision;
    private array $personalUse = [];

    public function __construct()
    {
        $this->areaPrecision = config('payroll.precision.area', 3);
        $this->moneyPrecision = config('payroll.precision.money', 2);
        $this->rentaNatPrecision = config('payroll.precision.rent_natura', 3);
    }

    /**
     * Execute the plot data retrieval with rent calculations
     */
    public function execute(
        int $year,
        ?int $contractId = null,
        ?int $annexId = null,
        ?int $ownerId = null,
        ?string $path = null,
        array $filterParams = []
    ): Collection {
        $contractAnnexId = !empty($annexId) ? $annexId : $contractId;

        // Get plot data from database
        $plots = $this->getPaymentPlots($year, $contractAnnexId, $ownerId, $path, $filterParams);

        // Get natura rent types
        $rentaNatArr = $this->getNatRents();

        // Get personal use data
        $this->personalUse = $this->getPersonalUseForOwners([
            'contract_id' => $contractAnnexId,
            'year' => $year,
            'chosen_years' => $year,
        ]);

        // Process each plot
        $processedPlots = $plots->map(function ($plot) use ($year, $rentaNatArr) {
            return $this->processPlot($plot, $year, $rentaNatArr);
        });

        return $processedPlots;
    }

    /**
     * Get payment plots from database
     */
    private function getPaymentPlots(
        int $year,
        ?int $contractAnnexId = null,
        ?int $ownerId = null,
        ?string $path = null,
        array $filterParams = []
    ): Collection {
        $query = DB::table('su_plots as p')
            ->join('su_plots_owners_rel as por', 'p.id', '=', 'por.plot_id')
            ->join('su_contracts_plots_rel as cpr', 'por.id', '=', 'cpr.pc_rel_id')
            ->join('su_contracts as c', 'cpr.contract_id', '=', 'c.id')
            ->join('su_owners as o', 'por.owner_id', '=', 'o.id')
            ->leftJoin('su_contracts_rents as cr', 'c.id', '=', 'cr.contract_id')
            ->leftJoin('su_contracts_charged_renta as ccr', function ($join) use ($year) {
                $join->on('c.id', '=', 'ccr.contract_id')
                     ->where('ccr.farming_year', '=', $year);
            })
            ->select([
                'p.id as plot_id',
                'p.kad_ident',
                'p.ekatte_name',
                'p.mestnost',
                'p.category',
                'p.area as plot_area',
                'o.id as owner_id',
                'o.name as owner_names',
                'o.egn as egn_eik',
                'o.is_dead',
                'o.dead_date',
                'o.phone',
                'o.mobile',
                'o.address',
                'o.iban',
                'por.path',
                'por.plots_percent',
                'por.id as pc_rel_id',
                'c.id as contract_id',
                'c.parent_id',
                'c.c_num',
                'c.farming_id',
                'c.contract_start_date',
                'c.contract_due_date',
                'cr.rent_money_value',
                'cr.rent_per_plot_value',
                'ccr.charged_renta_value',
                DB::raw('CONCAT(o.id, "_", COALESCE(CAST(por.path AS CHAR), "0")) as owner_path_key'),
                DB::raw('por.plots_percent / 100 * p.area as plot_owned_area'),
                DB::raw('por.plots_percent / 100 * p.area as calculation_area'),
            ]);

        // Apply filters
        if ($contractAnnexId) {
            $query->where('c.id', $contractAnnexId);
        }

        if ($ownerId) {
            $query->where('o.id', $ownerId);
        }

        if ($path) {
            $query->where('por.path', $path);
        }

        // Apply additional filter parameters
        if (!empty($filterParams['payroll_farming'])) {
            $query->whereIn('c.farming_id', $filterParams['payroll_farming']);
        }

        return $query->get();
    }

    /**
     * Get natura rent types
     */
    private function getNatRents(): Collection
    {
        return DB::table('su_renta_types as rt')
            ->join('su_units_of_measure as uom', 'rt.unit', '=', 'uom.id')
            ->select([
                'rt.id as renta_nat_id',
                'rt.name as renta_nat_name',
                'rt.unit_value',
                'rt.unit as unit_id',
                'uom.name as unit_name',
            ])
            ->get();
    }

    /**
     * Get personal use data for owners
     */
    private function getPersonalUseForOwners(array $criteria): array
    {
        // This would typically query a personal use table
        // For now, return empty array as placeholder
        return [];
    }

    /**
     * Process individual plot data
     */
    private function processPlot(object $plot, int $year, Collection $rentaNatArr): array
    {
        $plotData = (array) $plot;

        // Calculate areas
        $plotData['pu_area'] = $this->getPersonalUseArea($this->personalUse, $plot->owner_id, $plot);
        $plotData['calculation_area'] = Math::sub($plot->plot_owned_area, $plotData['pu_area'], $this->areaPrecision);
        $plotData['all_owner_area'] = Math::round($plot->plot_owned_area, $this->areaPrecision);
        $plotData['owner_area'] = Math::round($plotData['calculation_area'], $this->areaPrecision);

        // Initialize rent arrays
        $plotData['renta_nat'] = [];
        $plotData['renta_nat_info'] = [];
        $plotData['unpaid_renta_nat_arr'] = [];
        $plotData['overpaid_renta_nat_arr'] = [];
        $plotData['unpaid_renta_nat_money_arr'] = [];
        $plotData['overpaid_renta_nat_money_arr'] = [];
        $plotData['paid_nat_via_nat'] = [];
        $plotData['paid_renta_nat_sum'] = [];

        // Process natura rents
        foreach ($rentaNatArr as $rentaNat) {
            $this->processNaturaRent($plotData, $rentaNat);
        }

        // Calculate monetary rent
        $this->calculateMonetaryRent($plotData, $plot);

        return $plotData;
    }

    /**
     * Process natura rent for a plot
     */
    private function processNaturaRent(array &$plotData, object $rentaNat): void
    {
        $rentaNatId = $rentaNat->renta_nat_id;
        
        $plotData['renta_nat'][$rentaNatId] = 0;
        $plotData['renta_nat_info'][$rentaNatId] = [
            'renta_nat_id' => $rentaNatId,
            'renta_nat_name' => $rentaNat->renta_nat_name,
            'unit_id' => $rentaNat->unit_id,
            'unit_name' => $rentaNat->unit_name,
            'unit_value' => $rentaNat->unit_value,
            'charged_renta_nat' => null,
            'contract_renta_nat' => null,
        ];

        // Initialize arrays for this rent type
        $plotData['unpaid_renta_nat_arr'][$rentaNatId] = Math::round(0, $this->rentaNatPrecision);
        $plotData['overpaid_renta_nat_arr'][$rentaNatId] = Math::round(0, $this->rentaNatPrecision);
        $plotData['unpaid_renta_nat_money_arr'][$rentaNatId] = Math::round(0, $this->moneyPrecision);
        $plotData['overpaid_renta_nat_money_arr'][$rentaNatId] = Math::round(0, $this->moneyPrecision);
        $plotData['paid_nat_via_nat'][$rentaNatId] = Math::round(0, $this->rentaNatPrecision);
        $plotData['paid_renta_nat_sum'][$rentaNatId] = Math::round(0, $this->rentaNatPrecision);
    }

    /**
     * Calculate monetary rent for a plot
     */
    private function calculateMonetaryRent(array &$plotData, object $plot): void
    {
        // Calculate rent based on priority: individual rent > charged rent > contract rent
        if ($plot->rent_per_plot_value !== null) {
            $plotData['contract_renta'] = Math::mul($plot->rent_per_plot_value, $plotData['calculation_area']);
            $plotData['renta'] = $plotData['contract_renta'];
        } elseif ($plot->charged_renta_value !== null) {
            $chargedRenta = Math::mul($plot->charged_renta_value, $plotData['calculation_area']);
            $plotData['charged_renta'] = $chargedRenta;
        } else {
            $plotData['contract_renta'] = Math::mul($plot->rent_money_value ?? 0, $plotData['calculation_area']);
            $plotData['renta'] = $plotData['contract_renta'];
        }

        // Calculate unpaid rent
        $plotData['unpaid_renta'] = Math::add(
            $plotData['contract_renta'] ?? 0,
            $plotData['charged_renta'] ?? 0
        );

        // Initialize overpaid rent
        $plotData['overpaid_renta'] = Math::round(0, $this->moneyPrecision);
    }

    /**
     * Get personal use area for owner and plot
     */
    private function getPersonalUseArea(array $personalUse, int $ownerId, object $plot): float
    {
        // Placeholder implementation - would calculate personal use area
        // based on personal use data and plot information
        return 0.0;
    }
}
