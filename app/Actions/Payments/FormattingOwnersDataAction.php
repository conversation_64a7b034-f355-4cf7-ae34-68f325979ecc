<?php

declare(strict_types=1);

namespace App\Actions\Payments;

use App\Utils\Math;
use App\Utils\PayrollHelper;
use Illuminate\Support\Facades\DB;

/**
 * Formatting Owners Data Action
 *
 * Formats owner data for display in the payroll system.
 * This is the Laravel migration of the legacy formattingOwnersData() method.
 */
class FormattingOwnersDataAction
{
    private int $moneyPrecision;
    private int $areaPrecision;

    public function __construct()
    {
        $this->moneyPrecision = config('payroll.precision.money', 2);
        $this->areaPrecision = config('payroll.precision.area', 3);
    }

    /**
     * Execute data formatting for owners
     */
    public function execute(array $ownersData): array
    {
        $formattedData = [];

        foreach ($ownersData as $ownerKey => $ownerData) {
            $formattedData[] = $this->formatOwnerData($ownerData, $ownerKey);
        }

        return $formattedData;
    }

    /**
     * Format individual owner data
     */
    private function formatOwnerData(array $ownerData, string $ownerKey): array
    {
        // Get area types for formatting
        $areaTypes = $this->getAreaTypes();

        $formatted = [
            'id' => $ownerData['id'] ?? $ownerKey,
            'uuid' => $ownerData['uuid'] ?? PayrollHelper::generateUuid($ownerKey),
            'owner_id' => $ownerData['owner_id'],
            'owner_names' => $this->formatOwnerNames($ownerData),
            'owner_path_key' => $ownerData['owner_path_key'] ?? PayrollHelper::generateOwnerPathKey($ownerData['owner_id'], $ownerData['path'] ?? null),
            'path' => $ownerData['path'] ?? null,
            
            // Contract information
            'contract_id' => $ownerData['contract_id'] ?? null,
            'parent_id' => $ownerData['parent_id'] ?? null,
            'c_num' => $ownerData['c_num'] ?? '',
            'c_num_with_group_name' => $this->formatContractNumber($ownerData),
            'contract_group_name' => $ownerData['contract_group_name'] ?? '',
            'farming_name' => $ownerData['farming_name'] ?? '',
            'contract_start_date' => $this->formatDate($ownerData['contract_start_date'] ?? null),
            'contract_due_date' => $this->formatDate($ownerData['contract_due_date'] ?? null),
            
            // Owner personal information
            'egn_eik' => $ownerData['egn_eik'] ?? '',
            'phone' => $ownerData['phone'] ?? '',
            'mobile' => $ownerData['mobile'] ?? '',
            'address' => $ownerData['address'] ?? '',
            'iban' => $ownerData['iban'] ?? '',
            'is_dead' => $ownerData['is_dead'] ?? false,
            'dead_date' => $this->formatDate($ownerData['dead_date'] ?? null),
            
            // Plot information
            'plot_id' => $ownerData['plot_id'] ?? null,
            'kad_ident' => $this->formatKadIdents($ownerData),
            'ekatte_name' => $ownerData['ekatte_name'] ?? '',
            'mestnost' => $ownerData['mestnost'] ?? '',
            'category' => $ownerData['category'] ?? '',
            'rent_place_name' => $ownerData['rent_place_name'] ?? '',
            
            // Areas (formatted for display)
            'all_owner_area' => $this->formatArea($ownerData['all_owner_area'] ?? 0),
            'all_owner_contract_area' => $this->formatArea($ownerData['all_owner_contract_area'] ?? 0),
            'owner_area' => $this->formatArea($ownerData['owner_area'] ?? 0),
            'pu_area' => $this->formatArea($ownerData['pu_area'] ?? 0),
            'cultivated_area' => $this->formatArea($ownerData['cultivated_area'] ?? 0),
            
            // Raw area values for calculations
            'all_owner_area_raw' => $ownerData['all_owner_area'] ?? 0,
            'all_owner_contract_area_raw' => $ownerData['all_owner_contract_area'] ?? 0,
            'owner_area_raw' => $ownerData['owner_area'] ?? 0,
            'pu_area_raw' => $ownerData['pu_area'] ?? 0,
            'cultivated_area_raw' => $ownerData['cultivated_area'] ?? 0,
            
            // Monetary rents (formatted for display)
            'renta' => $this->formatCurrency($ownerData['renta'] ?? 0),
            'charged_renta' => $this->formatCurrency($ownerData['charged_renta'] ?? 0),
            'paid_renta' => $this->formatCurrency($ownerData['paid_renta'] ?? 0),
            'unpaid_renta' => $this->formatCurrency($ownerData['unpaid_renta'] ?? 0),
            'overpaid_renta' => $this->formatCurrency($ownerData['overpaid_renta'] ?? 0),
            'paid_via_money' => $this->formatCurrency($ownerData['paid_via_money'] ?? 0),
            'paid_via_nat' => $this->formatCurrency($ownerData['paid_via_nat'] ?? 0),
            
            // Raw monetary values for calculations
            'renta_raw' => $ownerData['renta'] ?? 0,
            'charged_renta_raw' => $ownerData['charged_renta'] ?? 0,
            'paid_renta_raw' => $ownerData['paid_renta'] ?? 0,
            'unpaid_renta_raw' => $ownerData['unpaid_renta'] ?? 0,
            'overpaid_renta_raw' => $ownerData['overpaid_renta'] ?? 0,
            'paid_via_money_raw' => $ownerData['paid_via_money'] ?? 0,
            'paid_via_nat_raw' => $ownerData['paid_via_nat'] ?? 0,
            
            // Natura rent information
            'renta_nat_info' => $this->formatNaturaRentInfo($ownerData['renta_nat_info'] ?? []),
            'unpaid_renta_nat_arr' => $ownerData['unpaid_renta_nat_arr'] ?? [],
            'overpaid_renta_nat_arr' => $ownerData['overpaid_renta_nat_arr'] ?? [],
            'unpaid_renta_nat_money_arr' => $ownerData['unpaid_renta_nat_money_arr'] ?? [],
            'overpaid_renta_nat_money_arr' => $ownerData['overpaid_renta_nat_money_arr'] ?? [],
            'paid_nat_via_nat' => $ownerData['paid_nat_via_nat'] ?? [],
            'paid_renta_nat_sum' => $ownerData['paid_renta_nat_sum'] ?? [],
            
            // Personal use information
            'personal_use_nat_types_names_arr' => $ownerData['personal_use_nat_types_names_arr'] ?? [],
            'personal_use_renta_arr' => $ownerData['personal_use_renta_arr'] ?? [],
            'personal_use_paid_renta_arr' => $ownerData['personal_use_paid_renta_arr'] ?? [],
            'personal_use_unpaid_renta_arr' => $ownerData['personal_use_unpaid_renta_arr'] ?? [],
            
            // Additional arrays for export
            'kad_idents_array' => $ownerData['kad_idents_array'] ?? [],
            'rep_names_array' => $ownerData['rep_names_array'] ?? [],
            'rep_ibans_array' => $ownerData['rep_ibans_array'] ?? [],
            
            // Currency conversion (if needed)
            'renta_eur' => $this->convertToEur($ownerData['renta'] ?? 0),
            'paid_renta_eur' => $this->convertToEur($ownerData['paid_renta'] ?? 0),
            'unpaid_renta_eur' => $this->convertToEur($ownerData['unpaid_renta'] ?? 0),
        ];

        // Add children data if present (for tree structures)
        if (isset($ownerData['children']) && is_array($ownerData['children'])) {
            $formatted['children'] = $this->execute($ownerData['children']);
        }

        return $formatted;
    }

    /**
     * Format owner names
     */
    private function formatOwnerNames(array $ownerData): string
    {
        $names = $ownerData['owner_names'] ?? '';
        
        // Add parent names if available
        if (!empty($ownerData['owner_parent_names'])) {
            $names .= ' (' . $ownerData['owner_parent_names'] . ')';
        }
        
        return trim($names);
    }

    /**
     * Format contract number with group name
     */
    private function formatContractNumber(array $ownerData): string
    {
        $cNum = $ownerData['c_num'] ?? '';
        $groupName = $ownerData['contract_group_name'] ?? '';
        
        if (!empty($groupName)) {
            return $cNum . ' (' . $groupName . ')';
        }
        
        return $cNum;
    }

    /**
     * Format cadastral identifiers
     */
    private function formatKadIdents(array $ownerData): string
    {
        if (isset($ownerData['kad_idents_array']) && is_array($ownerData['kad_idents_array'])) {
            return implode(', ', array_filter($ownerData['kad_idents_array']));
        }
        
        return $ownerData['kad_ident'] ?? '';
    }

    /**
     * Format area for display
     */
    private function formatArea(float $area): string
    {
        return PayrollHelper::formatArea($area);
    }

    /**
     * Format currency for display
     */
    private function formatCurrency(float $amount): string
    {
        return PayrollHelper::formatCurrency($amount);
    }

    /**
     * Format date for display
     */
    private function formatDate(?string $date): ?string
    {
        if (!$date) {
            return null;
        }
        
        try {
            return \Carbon\Carbon::parse($date)->format(config('payroll.display.date_format', 'd.m.Y'));
        } catch (\Exception $e) {
            return $date;
        }
    }

    /**
     * Convert BGN to EUR
     */
    private function convertToEur(float $amount): string
    {
        $eurAmount = PayrollHelper::bgnToEuro($amount);
        return number_format($eurAmount, $this->moneyPrecision, ',', ' ') . ' €';
    }

    /**
     * Format natura rent information
     */
    private function formatNaturaRentInfo(array $naturaRentInfo): array
    {
        $formatted = [];
        
        foreach ($naturaRentInfo as $rentaNatId => $info) {
            $formatted[$rentaNatId] = [
                'renta_nat_id' => $info['renta_nat_id'],
                'renta_nat_name' => $info['renta_nat_name'] ?? '',
                'unit_name' => $info['unit_name'] ?? '',
                'unit_value' => $this->formatCurrency($info['unit_value'] ?? 0),
                'unit_value_raw' => $info['unit_value'] ?? 0,
                'contract_renta_nat' => $info['contract_renta_nat'] ?? 0,
                'charged_renta_nat' => $info['charged_renta_nat'] ?? 0,
            ];
        }
        
        return $formatted;
    }

    /**
     * Get area types for formatting
     */
    private function getAreaTypes(): array
    {
        // Cache area types to avoid repeated queries
        static $areaTypes = null;
        
        if ($areaTypes === null) {
            $areaTypes = DB::table('su_area_types')
                ->select('id', 'name', 'short_name')
                ->get()
                ->keyBy('id')
                ->toArray();
        }
        
        return $areaTypes;
    }

    /**
     * Calculate totals for footer
     */
    public function calculateTotals(array $formattedData): array
    {
        $totals = [
            'total_owners' => count($formattedData),
            'total_area' => 0.0,
            'total_renta' => 0.0,
            'total_paid_renta' => 0.0,
            'total_unpaid_renta' => 0.0,
        ];

        foreach ($formattedData as $owner) {
            $totals['total_area'] = Math::add($totals['total_area'], $owner['owner_area_raw'] ?? 0);
            $totals['total_renta'] = Math::add($totals['total_renta'], $owner['renta_raw'] ?? 0);
            $totals['total_paid_renta'] = Math::add($totals['total_paid_renta'], $owner['paid_renta_raw'] ?? 0);
            $totals['total_unpaid_renta'] = Math::add($totals['total_unpaid_renta'], $owner['unpaid_renta_raw'] ?? 0);
        }

        // Format totals for display
        return [
            'total_owners' => $totals['total_owners'],
            'total_area' => $this->formatArea($totals['total_area']),
            'total_renta' => $this->formatCurrency($totals['total_renta']),
            'total_paid_renta' => $this->formatCurrency($totals['total_paid_renta']),
            'total_unpaid_renta' => $this->formatCurrency($totals['total_unpaid_renta']),
            'total_area_raw' => $totals['total_area'],
            'total_renta_raw' => $totals['total_renta'],
            'total_paid_renta_raw' => $totals['total_paid_renta'],
            'total_unpaid_renta_raw' => $totals['total_unpaid_renta'],
        ];
    }
}
