<?php

declare(strict_types=1);

namespace App\Actions\Payments;

use App\Utils\Math;
use Illuminate\Support\Collection;

/**
 * Process Owner Personal Use Action
 *
 * Processes personal use data for owners in payroll calculations.
 * This is the Laravel migration of the legacy processOwnerPersonalUse() method.
 */
class ProcessOwnerPersonalUseAction
{
    private int $moneyPrecision;
    private array $personalUse = [];

    public function __construct()
    {
        $this->moneyPrecision = config('payroll.precision.money', 2);
    }

    /**
     * Execute personal use processing for owners
     */
    public function execute(Collection $owners, ?string $gridType = null): Collection
    {
        return $owners->map(function ($owner) use ($gridType) {
            return $this->processOwnerPersonalUse($owner, $gridType);
        });
    }

    /**
     * Process personal use for a single owner
     */
    private function processOwnerPersonalUse(array $owner, ?string $gridType = null): array
    {
        $partCoef = 1;

        // Calculate part coefficient for contract payments grid type
        if (in_array($gridType, ['contract_payments'])) {
            $partCoef = Math::div(
                $owner['plot_owned_area'] ?? 0,
                $owner['plot_owned_area_total'] ?? 1
            );
        }

        // Initialize personal use arrays
        $owner['personal_use'] = [];
        $owner['personal_use_nat_type_id'] = [];
        $owner['personal_use_nat_types_names_arr'] = [];
        $owner['personal_use_amount_arr'] = [];
        $owner['personal_use_price_sum'] = [];
        $owner['personal_use_paid_arr'] = [];
        $owner['personal_use_unpaid_arr'] = [];
        $owner['personal_use_total'] = [];

        // Process each personal use entry for this owner
        foreach ($this->personalUse as $personalUseValue) {
            if ($personalUseValue['owner_id'] === $owner['owner_id']) {
                $this->addPersonalUseData($owner, $personalUseValue, $partCoef);
            }
        }

        return $owner;
    }

    /**
     * Add personal use data to owner
     */
    private function addPersonalUseData(array &$owner, array $personalUseValue, float $partCoef): void
    {
        $owner['personal_use_nat_type_id'][] = $personalUseValue['renta_type'];
        $owner['personal_use_unit_value'][] = $personalUseValue['personal_use_unit_value'];
        $owner['personal_use_nat_types_names_arr'][] = $personalUseValue['renta_type_name'];

        // Calculate personal use amounts with part coefficient
        $owner['personal_use_renta_arr'][] = Math::mul(
            $personalUseValue['personal_use_renta'],
            $partCoef,
            $this->moneyPrecision
        );

        $owner['personal_use_paid_renta_arr'][] = Math::mul(
            $personalUseValue['personal_use_paid_renta'],
            $partCoef,
            $this->moneyPrecision
        );

        $owner['personal_use_unpaid_renta_arr'][] = Math::mul(
            $personalUseValue['personal_use_unpaid_renta'],
            $partCoef,
            $this->moneyPrecision
        );

        $owner['personal_use_treatments_sum_arr'][] = Math::mul(
            $personalUseValue['personal_use_treatments_sum'],
            $partCoef,
            $this->moneyPrecision
        );

        $owner['personal_use_paid_treatments_arr'][] = Math::mul(
            $personalUseValue['personal_use_paid_treatments'],
            $partCoef,
            $this->moneyPrecision
        );

        $owner['personal_use_unpaid_treatments_arr'][] = Math::mul(
            $personalUseValue['personal_use_unpaid_treatments'],
            $partCoef,
            $this->moneyPrecision
        );
    }

    /**
     * Set personal use data
     */
    public function setPersonalUse(array $personalUse): void
    {
        $this->personalUse = $personalUse;
    }

    /**
     * Get personal use data
     */
    public function getPersonalUse(): array
    {
        return $this->personalUse;
    }

    /**
     * Calculate total personal use value for owner
     */
    public function calculateTotalPersonalUse(array $owner): float
    {
        $total = 0.0;

        if (isset($owner['personal_use_renta_arr'])) {
            foreach ($owner['personal_use_renta_arr'] as $amount) {
                $total = Math::add($total, $amount, $this->moneyPrecision);
            }
        }

        return $total;
    }

    /**
     * Calculate total personal use treatments for owner
     */
    public function calculateTotalPersonalUseTreatments(array $owner): float
    {
        $total = 0.0;

        if (isset($owner['personal_use_treatments_sum_arr'])) {
            foreach ($owner['personal_use_treatments_sum_arr'] as $amount) {
                $total = Math::add($total, $amount, $this->moneyPrecision);
            }
        }

        return $total;
    }

    /**
     * Check if owner has personal use
     */
    public function hasPersonalUse(int $ownerId): bool
    {
        foreach ($this->personalUse as $personalUseValue) {
            if ($personalUseValue['owner_id'] === $ownerId) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get personal use by owner ID
     */
    public function getPersonalUseByOwner(int $ownerId): array
    {
        $ownerPersonalUse = [];

        foreach ($this->personalUse as $personalUseValue) {
            if ($personalUseValue['owner_id'] === $ownerId) {
                $ownerPersonalUse[] = $personalUseValue;
            }
        }

        return $ownerPersonalUse;
    }

    /**
     * Calculate personal use area for owner
     */
    public function calculatePersonalUseArea(int $ownerId, array $plotData): float
    {
        $personalUseArea = 0.0;
        $ownerPersonalUse = $this->getPersonalUseByOwner($ownerId);

        foreach ($ownerPersonalUse as $personalUse) {
            // Calculate area based on personal use data and plot information
            // This is a simplified calculation - actual implementation would be more complex
            if (isset($personalUse['area'])) {
                $personalUseArea = Math::add($personalUseArea, $personalUse['area']);
            }
        }

        return $personalUseArea;
    }
}
