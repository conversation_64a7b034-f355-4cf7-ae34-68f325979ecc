<?php

declare(strict_types=1);

namespace App\Actions\Payments;

use App\Utils\Math;
use App\Utils\PayrollHelper;
use Illuminate\Support\Collection;

/**
 * Aggregate Payment Plots Action
 *
 * Aggregates plot data by different criteria (owner, plot, contract, rent_type).
 * This is the Laravel migration of the legacy aggPlots() method.
 */
class AggregatePaymentPlotsAction
{
    private int $moneyPrecision;

    public function __construct()
    {
        $this->moneyPrecision = config('payroll.precision.money', 2);
    }

    /**
     * Execute plot aggregation
     */
    public function execute(Collection $plots, string $aggBy = 'owner'): array
    {
        $aggPlots = [];
        $aggPlotsByPlot = [];

        foreach ($plots as $key => $plot) {
            $aggKey = $this->generateAggregationKey($plot, $aggBy);
            $aggKeyByPlot = null;

            if ($aggBy === 'owner') {
                // When aggregating by owner, prevent duplicate cultivated area calculation
                $aggKeyByPlot = $aggKey . '_' . $plot['pc_rel_id'];
            }

            if (!isset($aggPlots[$aggKey])) {
                $aggPlots[$aggKey] = $this->initializeAggregatedPlot($plot, $key);
                
                if ($aggKeyByPlot && !in_array($aggKeyByPlot, $aggPlotsByPlot)) {
                    $aggPlotsByPlot[] = $aggKeyByPlot;
                }
            } else {
                $aggPlots[$aggKey] = $this->sumPayments(
                    $aggPlots[$aggKey],
                    $plot,
                    in_array($aggKeyByPlot, $aggPlotsByPlot)
                );

                if ($aggKeyByPlot && !in_array($aggKeyByPlot, $aggPlotsByPlot)) {
                    $aggPlotsByPlot[] = $aggKeyByPlot;
                }
            }

            // Add plot-specific arrays for export purposes
            $this->addPlotSpecificArrays($aggPlots[$aggKey], $plot);
        }

        return $aggPlots;
    }

    /**
     * Generate aggregation key based on aggregation type
     */
    private function generateAggregationKey(array $plot, string $aggBy): string
    {
        return match ($aggBy) {
            'owner' => $plot['owner_id'] . '_' . ($plot['path'] ?? '0'),
            'plot' => (string) $plot['pc_rel_id'],
            'contract' => (string) $plot['contract_id'],
            'rent_type' => $plot['owner_id'] . '_' . ($plot['plot_rent_type_id'] . '_' . $plot['pc_rel_id'] ?? '0'),
            default => $plot['owner_id'] . '_' . ($plot['path'] ?? '0'),
        };
    }

    /**
     * Initialize aggregated plot data structure
     */
    private function initializeAggregatedPlot(array $plot, int $key): array
    {
        return [
            'uuid' => PayrollHelper::generateUuid($plot['owner_id'] . ($plot['path'] ?? '')),
            'contract_id' => $plot['contract_id'],
            'parent_id' => $plot['parent_id'] ?? null,
            'c_num' => $plot['c_num'] ?? '',
            'c_num_with_group_name' => $plot['c_num'] ?? '',
            'contract_group_name' => $plot['contract_group_name'] ?? '',
            'egn_eik' => $plot['egn_eik'] ?? '',
            'farming_id' => $plot['farming_id'] ?? null,
            'farming_name' => $plot['farming_name'] ?? '',
            'contract_type' => $plot['contract_type'] ?? '',
            'contract_start_date' => $plot['contract_start_date'] ?? null,
            'contract_due_date' => $plot['contract_due_date'] ?? null,
            'id' => $key + 1, // DataGrid component requires unique ID
            'owner_id' => $plot['owner_id'],
            'owner_names' => $plot['owner_names'] ?? '',
            'owner_parent_names' => $plot['owner_parent_names'] ?? '',
            'owner_parent_id' => $plot['owner_parent_id'] ?? null,
            'phone' => $plot['phone'] ?? '',
            'mobile' => $plot['mobile'] ?? '',
            'address' => $plot['address'] ?? '',
            'iban' => $plot['iban'] ?? '',
            'rent_place_name' => $plot['rent_place_name'] ?? '',
            'is_dead' => $plot['is_dead'] ?? false,
            'dead_date' => $plot['dead_date'] ?? null,
            'plot_id' => $plot['plot_id'],
            'kad_ident' => $plot['kad_ident'] ?? '',
            'ekatte_name' => $plot['ekatte_name'] ?? '',
            'mestnost' => $plot['mestnost'] ?? '',
            'category' => $plot['category'] ?? '',
            'pc_rel_id' => $plot['pc_rel_id'],
            'plot_rent_type_id' => $plot['plot_rent_type_id'] ?? null,
            'path' => $plot['path'] ?? null,
            'owner_path_key' => $plot['owner_path_key'] ?? PayrollHelper::generateOwnerPathKey($plot['owner_id'], $plot['path'] ?? null),
            'all_owner_area' => $plot['all_owner_area'] ?? 0,
            'all_owner_contract_area' => $plot['all_owner_contract_area'] ?? 0,
            'plot_owned_area_total' => $plot['plot_owned_area_total'] ?? 0,
            'plot_owned_area' => $plot['plot_owned_area'] ?? 0,
            'pu_area' => $plot['pu_area'] ?? 0,
            'owner_area' => $plot['calculation_area'] ?? 0,
            'cultivated_area' => $plot['cultivated_area'] ?? 0,
            'renta' => $plot['renta'] ?? 0,
            'charged_renta' => $plot['charged_renta'] ?? 0,
            'paid_renta' => Math::round(0, $this->moneyPrecision),
            'paid_via_money' => Math::round(0, $this->moneyPrecision),
            'unpaid_renta' => $plot['unpaid_renta'] ?? 0,
            'overpaid_renta' => Math::round(0, $this->moneyPrecision),
            'rent_per_plot_value' => $plot['rent_per_plot_value'] ?? null,
            'rent_money_value' => $plot['rent_money_value'] ?? 0,
            'charged_renta_value' => $plot['charged_renta_value'] ?? null,
            'renta_nat' => $plot['renta_nat'] ?? [],
            'charged_renta_nat' => $plot['charged_renta_nat'] ?? [],
            'renta_nat_info' => $plot['renta_nat_info'] ?? [],
            'unpaid_renta_nat_arr' => $plot['unpaid_renta_nat_arr'] ?? [],
            'overpaid_renta_nat_arr' => $plot['overpaid_renta_nat_arr'] ?? [],
            'unpaid_renta_nat_money_arr' => $plot['unpaid_renta_nat_money_arr'] ?? [],
            'overpaid_renta_nat_money_arr' => $plot['overpaid_renta_nat_money_arr'] ?? [],
            'kad_idents_array' => [],
            'rep_names_array' => [],
            'rep_ibans_array' => [],
        ];
    }

    /**
     * Sum payments data for aggregation
     */
    private function sumPayments(array $payment1, array $payment2, bool $duplicatedPlot = false): array
    {
        // Add plot IDs
        if (!isset($payment1['plot_id']) || !is_array($payment1['plot_id'])) {
            $payment1['plot_id'] = [$payment1['plot_id']];
        }
        $payment1['plot_id'][] = $payment2['plot_id'];

        if (!isset($payment1['pc_rel_id']) || !is_array($payment1['pc_rel_id'])) {
            $payment1['pc_rel_id'] = [$payment1['pc_rel_id']];
        }
        $payment1['pc_rel_id'][] = $payment2['pc_rel_id'];

        // Sum areas
        $payment1['all_owner_area'] = Math::add($payment1['all_owner_area'], $payment2['all_owner_area'] ?? 0);
        $payment1['all_owner_contract_area'] = Math::add($payment1['all_owner_contract_area'], $payment2['all_owner_contract_area'] ?? 0);
        $payment1['pu_area'] = Math::add($payment1['pu_area'], $payment2['pu_area'] ?? 0);
        $payment1['owner_area'] = Math::add($payment1['owner_area'], $payment2['calculation_area'] ?? 0);

        // Sum rents
        $payment1['renta'] = Math::add($payment1['renta'], $payment2['renta'] ?? 0);
        $payment1['charged_renta'] = Math::add($payment1['charged_renta'], $payment2['charged_renta'] ?? 0);
        $payment1['unpaid_renta'] = Math::add($payment1['unpaid_renta'], $payment2['unpaid_renta'] ?? 0);

        // Sum natura rents
        if (isset($payment2['renta_nat_info'])) {
            foreach ($payment2['renta_nat_info'] as $rentaNatInfo) {
                $rentaNatId = $rentaNatInfo['renta_nat_id'];
                
                if (!isset($payment1['renta_nat'][$rentaNatId])) {
                    $payment1['renta_nat'][$rentaNatId] = 0;
                }
                if (!isset($payment1['charged_renta_nat'][$rentaNatId])) {
                    $payment1['charged_renta_nat'][$rentaNatId] = 0;
                }

                $payment1['renta_nat'][$rentaNatId] = Math::add(
                    $payment1['renta_nat'][$rentaNatId],
                    $rentaNatInfo['contract_renta_nat'] ?? 0
                );
                $payment1['charged_renta_nat'][$rentaNatId] = Math::add(
                    $payment1['charged_renta_nat'][$rentaNatId],
                    $rentaNatInfo['charged_renta_nat'] ?? 0
                );
            }
        }

        // Sum unpaid natura rents
        if (isset($payment2['unpaid_renta_nat_arr'])) {
            foreach ($payment2['unpaid_renta_nat_arr'] as $rentaNatId => $value) {
                if (!isset($payment1['unpaid_renta_nat_arr'][$rentaNatId])) {
                    $payment1['unpaid_renta_nat_arr'][$rentaNatId] = 0;
                }
                $payment1['unpaid_renta_nat_arr'][$rentaNatId] = Math::add(
                    $payment1['unpaid_renta_nat_arr'][$rentaNatId],
                    $value
                );
            }
        }

        // Sum unpaid natura rents in money
        if (isset($payment2['unpaid_renta_nat_money_arr'])) {
            foreach ($payment2['unpaid_renta_nat_money_arr'] as $rentaNatId => $value) {
                if (!isset($payment1['unpaid_renta_nat_money_arr'][$rentaNatId])) {
                    $payment1['unpaid_renta_nat_money_arr'][$rentaNatId] = 0;
                }
                $payment1['unpaid_renta_nat_money_arr'][$rentaNatId] = Math::add(
                    $payment1['unpaid_renta_nat_money_arr'][$rentaNatId],
                    $value
                );
            }
        }

        // Only add cultivated area if not duplicated plot
        if (!$duplicatedPlot) {
            $payment1['cultivated_area'] = Math::add(
                $payment1['cultivated_area'],
                $payment2['cultivated_area'] ?? 0
            );
        }

        return $payment1;
    }

    /**
     * Add plot-specific arrays for export purposes
     */
    private function addPlotSpecificArrays(array &$aggPlot, array $plot): void
    {
        // Add cadastral identifiers
        if (!is_array($aggPlot['kad_idents_array'])) {
            $aggPlot['kad_idents_array'] = [];
        }
        if (!in_array($plot['kad_ident'] ?? '', $aggPlot['kad_idents_array'])) {
            $aggPlot['kad_idents_array'][] = $plot['kad_ident'] ?? '';
        }

        // Add representative names
        if (!is_array($aggPlot['rep_names_array'])) {
            $aggPlot['rep_names_array'] = [];
        }
        if (!empty($plot['rep_names']) && !in_array($plot['rep_names'], $aggPlot['rep_names_array'])) {
            $aggPlot['rep_names_array'][] = $plot['rep_names'];
        }

        // Add representative IBANs
        if (!is_array($aggPlot['rep_ibans_array'])) {
            $aggPlot['rep_ibans_array'] = [];
        }
        if (!empty($plot['rep_iban']) && !in_array($plot['rep_iban'], $aggPlot['rep_ibans_array'])) {
            $aggPlot['rep_ibans_array'][] = $plot['rep_iban'];
        }
    }
}
