<?php

declare(strict_types=1);

namespace App\Actions\Payments;

use App\Utils\Math;
use Illuminate\Support\Facades\DB;

/**
 * Contracts Payments Mapping Action
 *
 * Maps contract payments to aggregated plots and calculates remaining rents.
 * This is the Laravel migration of the legacy contractsPaymentsMapping() method.
 */
class ContractsPaymentsMappingAction
{
    public function __construct(
        private GetContractOwnerPaymentsAction $getContractOwnerPaymentsAction
    ) {}

    /**
     * Execute contracts payments mapping
     */
    public function execute(
        array $aggPlots,
        array $aggPlotsByContract,
        int $year,
        ?int $ownerId = null,
        ?string $path = null
    ): array {
        foreach ($aggPlotsByContract as $contract) {
            $contractId = $contract['contract_id'];
            $annexId = null;
            $ownerPayment = null;

            // Handle contract annexes
            if ($contract['parent_id']) {
                $contractId = $contract['parent_id'];
                $annexId = $contract['contract_id'];
            }

            // Get contract payments data
            $contractPayments = $this->getContractPayments($year, $contractId, $annexId);
            
            if ($contractPayments) {
                $ownerPayment = $this->getOwnerDataFromContract($contractPayments, $path ?? $ownerId);
            }

            // Map payment data to aggregated plots
            foreach ($aggPlots as $plotKey => $plot) {
                if ($this->shouldMapPayment($plot, $contract)) {
                    $aggPlots[$plotKey] = $this->mapPaymentData($aggPlots[$plotKey], $ownerPayment);
                }
            }
        }

        return $aggPlots;
    }

    /**
     * Get contract payments data
     */
    private function getContractPayments(int $year, int $contractId, ?int $annexId = null): ?array
    {
        // This would call the GetPaymentsAction (equivalent to getPayments method)
        // For now, return null as placeholder
        return null;
    }

    /**
     * Get owner data from contract payments tree
     */
    private function getOwnerDataFromContract(array $contractPayments, int|string $ownerIdentifier): ?array
    {
        // This would recursively traverse the contract payments tree
        // to find data for the specific owner
        return $this->traverseContractTree($contractPayments, $ownerIdentifier);
    }

    /**
     * Recursively traverse contract tree to find owner data
     */
    private function traverseContractTree(array $contractData, int|string $ownerIdentifier): ?array
    {
        // Check if current node matches the owner identifier
        if ($this->matchesOwnerIdentifier($contractData, $ownerIdentifier)) {
            return $this->extractOwnerPaymentData($contractData);
        }

        // Recursively check children
        if (isset($contractData['children']) && is_array($contractData['children'])) {
            foreach ($contractData['children'] as $child) {
                $result = $this->traverseContractTree($child, $ownerIdentifier);
                if ($result !== null) {
                    return $result;
                }
            }
        }

        return null;
    }

    /**
     * Check if contract data matches owner identifier
     */
    private function matchesOwnerIdentifier(array $contractData, int|string $ownerIdentifier): bool
    {
        if (is_int($ownerIdentifier)) {
            return ($contractData['owner_id'] ?? null) === $ownerIdentifier;
        }

        // For path-based matching
        return ($contractData['path'] ?? null) === $ownerIdentifier ||
               ($contractData['owner_path_key'] ?? null) === $ownerIdentifier;
    }

    /**
     * Extract owner payment data from contract data
     */
    private function extractOwnerPaymentData(array $contractData): array
    {
        return [
            'paid_renta' => $contractData['paid_renta'] ?? 0,
            'unpaid_renta' => $contractData['unpaid_renta'] ?? 0,
            'overpaid_renta' => $contractData['overpaid_renta'] ?? 0,
            'paid_renta_by' => $contractData['paid_renta_by'] ?? '',
            'paid_nat_via_money' => $contractData['paid_nat_via_money'] ?? 0,
            'paid_nat_via_nat' => $contractData['paid_nat_via_nat'] ?? [],
            'paid_renta_nat_sum' => $contractData['paid_renta_nat_sum'] ?? [],
            'overpaid_renta_nat_money_arr' => $contractData['overpaid_renta_nat_money_arr'] ?? [],
            'unpaid_renta_nat_money_arr' => $contractData['unpaid_renta_nat_money_arr'] ?? [],
            'overpaid_renta_nat_arr' => $contractData['overpaid_renta_nat_arr'] ?? [],
            'unpaid_renta_nat_arr' => $contractData['unpaid_renta_nat_arr'] ?? [],
            'paid_via_nat' => $contractData['paid_via_nat'] ?? 0,
            'paid_via_money' => $contractData['paid_via_money'] ?? 0,
            'rent_place_name' => $contractData['rent_place_name'] ?? '',
        ];
    }

    /**
     * Check if payment should be mapped to plot
     */
    private function shouldMapPayment(array $plot, array $contract): bool
    {
        return $plot['contract_id'] == $contract['contract_id'] &&
               $plot['parent_id'] == $contract['parent_id'];
    }

    /**
     * Map payment data to aggregated plot
     */
    private function mapPaymentData(array $plot, ?array $ownerPayment): array
    {
        if ($ownerPayment === null) {
            return $plot;
        }

        // Map all payment-related fields
        $plot['paid_renta'] = $ownerPayment['paid_renta'];
        $plot['unpaid_renta'] = $ownerPayment['unpaid_renta'];
        $plot['overpaid_renta'] = $ownerPayment['overpaid_renta'];
        $plot['paid_renta_by'] = $ownerPayment['paid_renta_by'];
        $plot['paid_nat_via_money'] = $ownerPayment['paid_nat_via_money'];
        $plot['paid_nat_via_nat'] = $ownerPayment['paid_nat_via_nat'];
        $plot['paid_renta_nat_sum'] = $ownerPayment['paid_renta_nat_sum'];
        $plot['overpaid_renta_nat_money_arr'] = $ownerPayment['overpaid_renta_nat_money_arr'];
        $plot['unpaid_renta_nat_money_arr'] = $ownerPayment['unpaid_renta_nat_money_arr'];
        $plot['overpaid_renta_nat_arr'] = $ownerPayment['overpaid_renta_nat_arr'];
        $plot['unpaid_renta_nat_arr'] = $ownerPayment['unpaid_renta_nat_arr'];
        $plot['paid_via_nat'] = $ownerPayment['paid_via_nat'];
        $plot['paid_via_money'] = $ownerPayment['paid_via_money'];

        // Set rent place if not already set
        if (empty($plot['rent_place']) || $plot['rent_place'] === '-') {
            $plot['rent_place'] = $ownerPayment['rent_place_name'];
        }

        return $plot;
    }

    /**
     * Get contract owner payments using the dedicated action
     */
    private function getContractOwnerPayments(
        int $year,
        ?array $contractAnnexIds = null,
        ?string $ownerIdPath = null
    ): array {
        return $this->getContractOwnerPaymentsAction->execute($year, $contractAnnexIds, $ownerIdPath);
    }

    /**
     * Calculate payment proportions for plot distribution
     */
    private function calculatePaymentProportion(array $plot, array $contractData): float
    {
        // Calculate the proportion of the plot area relative to the total area
        // that the owner possesses from the contract
        $plotArea = $plot['owner_area'] ?? 0;
        $totalContractArea = $contractData['total_contract_area'] ?? 1;

        if ($totalContractArea == 0) {
            return 0.0;
        }

        return Math::div($plotArea, $totalContractArea);
    }

    /**
     * Distribute payment proportionally across plots
     */
    private function distributePaymentProportionally(array $plot, array $ownerPayment, float $proportion): array
    {
        // Distribute monetary payments
        $plot['paid_renta'] = Math::mul($ownerPayment['paid_renta'] ?? 0, $proportion);
        $plot['unpaid_renta'] = Math::mul($ownerPayment['unpaid_renta'] ?? 0, $proportion);
        $plot['overpaid_renta'] = Math::mul($ownerPayment['overpaid_renta'] ?? 0, $proportion);

        // Distribute natura payments
        if (isset($ownerPayment['paid_nat_via_nat']) && is_array($ownerPayment['paid_nat_via_nat'])) {
            foreach ($ownerPayment['paid_nat_via_nat'] as $rentaNatId => $amount) {
                $plot['paid_nat_via_nat'][$rentaNatId] = Math::mul($amount, $proportion);
            }
        }

        return $plot;
    }
}
