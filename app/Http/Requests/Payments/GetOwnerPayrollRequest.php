<?php

declare(strict_types=1);

namespace App\Http\Requests\Payments;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Get Owner Payroll Request
 *
 * Validation rules for owner payroll requests.
 * Handles validation of parameters for the getOwnerPayroll functionality.
 */
class GetOwnerPayrollRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Add authorization logic here if needed
        // For now, allow all authenticated users
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'year' => [
                'required',
                'integer',
                'min:2000',
                'max:2100',
            ],
            'owner_id' => [
                'nullable',
                'integer',
                'min:1',
            ],
            'path' => [
                'nullable',
                'string',
                'max:255',
            ],
            'filter_params' => [
                'nullable',
                'array',
            ],
            'filter_params.payroll_farming' => [
                'nullable',
                'array',
            ],
            'filter_params.payroll_farming.*' => [
                'integer',
                'min:1',
            ],
            'filter_params.owner_ids' => [
                'nullable',
                'array',
            ],
            'filter_params.owner_ids.*' => [
                'integer',
                'min:1',
            ],
            'filter_params.owner_names' => [
                'nullable',
                'string',
                'max:255',
            ],
            'filter_params.egn' => [
                'nullable',
                'string',
                'max:20',
            ],
            'filter_params.heritor_names' => [
                'nullable',
                'string',
                'max:255',
            ],
            'filter_params.heritor_egn' => [
                'nullable',
                'string',
                'max:20',
            ],
            'filter_params.contract_id' => [
                'nullable',
                'integer',
                'min:1',
            ],
            'filter_params.ekatte' => [
                'nullable',
                'string',
                'max:10',
            ],
            'filter_params.kad_ident' => [
                'nullable',
                'string',
                'max:50',
            ],
            'filter_params.no_contract_payments' => [
                'nullable',
                'boolean',
            ],
            'format' => [
                'nullable',
                'string',
                'in:json,csv,excel,pdf',
            ],
            'page' => [
                'nullable',
                'integer',
                'min:1',
            ],
            'per_page' => [
                'nullable',
                'integer',
                'min:1',
                'max:1000',
            ],
            'sort' => [
                'nullable',
                'string',
                'in:owner_names,egn_eik,owner_area,renta,paid_renta,unpaid_renta,contract_number',
            ],
            'order' => [
                'nullable',
                'string',
                'in:asc,desc',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'year.required' => 'Farming year is required.',
            'year.integer' => 'Farming year must be a valid integer.',
            'year.min' => 'Farming year must be at least 2000.',
            'year.max' => 'Farming year cannot be greater than 2100.',
            'owner_id.integer' => 'Owner ID must be a valid integer.',
            'owner_id.min' => 'Owner ID must be greater than 0.',
            'path.string' => 'Path must be a valid string.',
            'path.max' => 'Path cannot be longer than 255 characters.',
            'filter_params.array' => 'Filter parameters must be an array.',
            'filter_params.payroll_farming.array' => 'Payroll farming filter must be an array.',
            'filter_params.payroll_farming.*.integer' => 'Each payroll farming ID must be a valid integer.',
            'filter_params.owner_ids.array' => 'Owner IDs filter must be an array.',
            'filter_params.owner_ids.*.integer' => 'Each owner ID must be a valid integer.',
            'filter_params.owner_names.string' => 'Owner names filter must be a string.',
            'filter_params.egn.string' => 'EGN filter must be a string.',
            'filter_params.egn.max' => 'EGN filter cannot be longer than 20 characters.',
            'filter_params.contract_id.integer' => 'Contract ID must be a valid integer.',
            'filter_params.ekatte.string' => 'EKATTE filter must be a string.',
            'filter_params.ekatte.max' => 'EKATTE filter cannot be longer than 10 characters.',
            'filter_params.kad_ident.string' => 'Cadastral identifier filter must be a string.',
            'filter_params.kad_ident.max' => 'Cadastral identifier filter cannot be longer than 50 characters.',
            'format.in' => 'Format must be one of: json, csv, excel, pdf.',
            'page.integer' => 'Page must be a valid integer.',
            'page.min' => 'Page must be at least 1.',
            'per_page.integer' => 'Per page must be a valid integer.',
            'per_page.min' => 'Per page must be at least 1.',
            'per_page.max' => 'Per page cannot be greater than 1000.',
            'sort.in' => 'Sort field must be one of the allowed values.',
            'order.in' => 'Order must be either asc or desc.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'year' => 'farming year',
            'owner_id' => 'owner ID',
            'path' => 'owner path',
            'filter_params' => 'filter parameters',
            'filter_params.payroll_farming' => 'payroll farming filter',
            'filter_params.owner_ids' => 'owner IDs filter',
            'filter_params.owner_names' => 'owner names filter',
            'filter_params.egn' => 'EGN filter',
            'filter_params.heritor_names' => 'heritor names filter',
            'filter_params.heritor_egn' => 'heritor EGN filter',
            'filter_params.contract_id' => 'contract ID filter',
            'filter_params.ekatte' => 'EKATTE filter',
            'filter_params.kad_ident' => 'cadastral identifier filter',
            'format' => 'export format',
            'page' => 'page number',
            'per_page' => 'items per page',
            'sort' => 'sort field',
            'order' => 'sort order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert string values to appropriate types
        if ($this->has('year') && is_string($this->year)) {
            $this->merge(['year' => (int) $this->year]);
        }

        if ($this->has('owner_id') && is_string($this->owner_id)) {
            $this->merge(['owner_id' => (int) $this->owner_id]);
        }

        if ($this->has('page') && is_string($this->page)) {
            $this->merge(['page' => (int) $this->page]);
        }

        if ($this->has('per_page') && is_string($this->per_page)) {
            $this->merge(['per_page' => (int) $this->per_page]);
        }

        // Set default values
        if (!$this->has('format')) {
            $this->merge(['format' => 'json']);
        }

        if (!$this->has('page')) {
            $this->merge(['page' => 1]);
        }

        if (!$this->has('per_page')) {
            $this->merge(['per_page' => 50]);
        }

        if (!$this->has('order')) {
            $this->merge(['order' => 'asc']);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Custom validation logic
            $this->validateOwnerPathCombination($validator);
            $this->validateFilterParams($validator);
        });
    }

    /**
     * Validate owner ID and path combination
     */
    private function validateOwnerPathCombination($validator): void
    {
        $ownerId = $this->input('owner_id');
        $path = $this->input('path');

        // If path is provided, owner_id should also be provided or derivable
        if ($path && !$ownerId) {
            // Try to extract owner_id from path if it's in owner_id_path format
            if (str_contains($path, '_')) {
                $pathParts = explode('_', $path, 2);
                if (is_numeric($pathParts[0])) {
                    $this->merge(['owner_id' => (int) $pathParts[0]]);
                }
            }
        }
    }

    /**
     * Validate filter parameters
     */
    private function validateFilterParams($validator): void
    {
        $filterParams = $this->input('filter_params', []);

        // Validate that at least one meaningful parameter is provided
        $hasValidParams = $this->has('year') ||
                         $this->has('owner_id') ||
                         !empty($filterParams['payroll_farming']) ||
                         !empty($filterParams['owner_ids']);

        if (!$hasValidParams) {
            $validator->errors()->add('year', 'At least farming year must be provided.');
        }
    }
}
