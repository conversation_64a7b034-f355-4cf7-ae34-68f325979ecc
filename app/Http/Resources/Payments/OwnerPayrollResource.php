<?php

declare(strict_types=1);

namespace App\Http\Resources\Payments;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Owner Payroll Resource
 *
 * API resource for formatting owner payroll data responses.
 * Provides structured output for the migrated getOwnerPayroll functionality.
 */
class OwnerPayrollResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource['id'] ?? null,
            'uuid' => $this->resource['uuid'] ?? null,
            
            // Owner information
            'owner' => [
                'id' => $this->resource['owner_id'],
                'names' => $this->resource['owner_names'] ?? '',
                'egn_eik' => $this->resource['egn_eik'] ?? '',
                'phone' => $this->resource['phone'] ?? '',
                'mobile' => $this->resource['mobile'] ?? '',
                'address' => $this->resource['address'] ?? '',
                'iban' => $this->resource['iban'] ?? '',
                'is_dead' => $this->resource['is_dead'] ?? false,
                'dead_date' => $this->resource['dead_date'] ?? null,
                'path' => $this->resource['path'] ?? null,
                'path_key' => $this->resource['owner_path_key'] ?? null,
            ],

            // Contract information
            'contract' => [
                'id' => $this->resource['contract_id'] ?? null,
                'parent_id' => $this->resource['parent_id'] ?? null,
                'number' => $this->resource['c_num'] ?? '',
                'number_with_group' => $this->resource['c_num_with_group_name'] ?? '',
                'group_name' => $this->resource['contract_group_name'] ?? '',
                'farming_name' => $this->resource['farming_name'] ?? '',
                'start_date' => $this->resource['contract_start_date'] ?? null,
                'due_date' => $this->resource['contract_due_date'] ?? null,
            ],

            // Plot information
            'plot' => [
                'id' => $this->resource['plot_id'] ?? null,
                'kad_ident' => $this->resource['kad_ident'] ?? '',
                'kad_idents_array' => $this->resource['kad_idents_array'] ?? [],
                'ekatte_name' => $this->resource['ekatte_name'] ?? '',
                'mestnost' => $this->resource['mestnost'] ?? '',
                'category' => $this->resource['category'] ?? '',
                'rent_place_name' => $this->resource['rent_place_name'] ?? '',
            ],

            // Area information
            'areas' => [
                'all_owner_area' => [
                    'formatted' => $this->resource['all_owner_area'] ?? '0,000 дка',
                    'raw' => $this->resource['all_owner_area_raw'] ?? 0,
                ],
                'all_owner_contract_area' => [
                    'formatted' => $this->resource['all_owner_contract_area'] ?? '0,000 дка',
                    'raw' => $this->resource['all_owner_contract_area_raw'] ?? 0,
                ],
                'owner_area' => [
                    'formatted' => $this->resource['owner_area'] ?? '0,000 дка',
                    'raw' => $this->resource['owner_area_raw'] ?? 0,
                ],
                'personal_use_area' => [
                    'formatted' => $this->resource['pu_area'] ?? '0,000 дка',
                    'raw' => $this->resource['pu_area_raw'] ?? 0,
                ],
                'cultivated_area' => [
                    'formatted' => $this->resource['cultivated_area'] ?? '0,000 дка',
                    'raw' => $this->resource['cultivated_area_raw'] ?? 0,
                ],
            ],

            // Monetary rent information
            'rent' => [
                'total_rent' => [
                    'formatted' => $this->resource['renta'] ?? '0,00 лв.',
                    'raw' => $this->resource['renta_raw'] ?? 0,
                    'eur' => $this->resource['renta_eur'] ?? '0,00 €',
                ],
                'charged_rent' => [
                    'formatted' => $this->resource['charged_renta'] ?? '0,00 лв.',
                    'raw' => $this->resource['charged_renta_raw'] ?? 0,
                ],
                'paid_rent' => [
                    'formatted' => $this->resource['paid_renta'] ?? '0,00 лв.',
                    'raw' => $this->resource['paid_renta_raw'] ?? 0,
                    'eur' => $this->resource['paid_renta_eur'] ?? '0,00 €',
                ],
                'unpaid_rent' => [
                    'formatted' => $this->resource['unpaid_renta'] ?? '0,00 лв.',
                    'raw' => $this->resource['unpaid_renta_raw'] ?? 0,
                    'eur' => $this->resource['unpaid_renta_eur'] ?? '0,00 €',
                ],
                'overpaid_rent' => [
                    'formatted' => $this->resource['overpaid_renta'] ?? '0,00 лв.',
                    'raw' => $this->resource['overpaid_renta_raw'] ?? 0,
                ],
                'paid_via_money' => [
                    'formatted' => $this->resource['paid_via_money'] ?? '0,00 лв.',
                    'raw' => $this->resource['paid_via_money_raw'] ?? 0,
                ],
                'paid_via_natura' => [
                    'formatted' => $this->resource['paid_via_nat'] ?? '0,00 лв.',
                    'raw' => $this->resource['paid_via_nat_raw'] ?? 0,
                ],
            ],

            // Natura rent information
            'natura_rent' => [
                'types' => $this->resource['renta_nat_info'] ?? [],
                'unpaid_amounts' => $this->resource['unpaid_renta_nat_arr'] ?? [],
                'overpaid_amounts' => $this->resource['overpaid_renta_nat_arr'] ?? [],
                'unpaid_money_amounts' => $this->resource['unpaid_renta_nat_money_arr'] ?? [],
                'overpaid_money_amounts' => $this->resource['overpaid_renta_nat_money_arr'] ?? [],
                'paid_via_natura' => $this->resource['paid_nat_via_nat'] ?? [],
                'paid_natura_sum' => $this->resource['paid_renta_nat_sum'] ?? [],
            ],

            // Personal use information
            'personal_use' => [
                'natura_types_names' => $this->resource['personal_use_nat_types_names_arr'] ?? [],
                'rent_amounts' => $this->resource['personal_use_renta_arr'] ?? [],
                'paid_rent_amounts' => $this->resource['personal_use_paid_renta_arr'] ?? [],
                'unpaid_rent_amounts' => $this->resource['personal_use_unpaid_renta_arr'] ?? [],
            ],

            // Representative information
            'representatives' => [
                'names' => $this->resource['rep_names_array'] ?? [],
                'ibans' => $this->resource['rep_ibans_array'] ?? [],
            ],

            // Payment status
            'payment_status' => $this->getPaymentStatus(),

            // Additional metadata
            'meta' => [
                'has_children' => isset($this->resource['children']) && !empty($this->resource['children']),
                'children_count' => isset($this->resource['children']) ? count($this->resource['children']) : 0,
                'is_deceased' => $this->resource['is_dead'] ?? false,
                'has_personal_use' => !empty($this->resource['pu_area_raw']),
                'has_natura_rent' => !empty($this->resource['renta_nat_info']),
            ],

            // Children (for tree structures)
            'children' => $this->when(
                isset($this->resource['children']) && !empty($this->resource['children']),
                function () {
                    return OwnerPayrollResource::collection($this->resource['children']);
                }
            ),
        ];
    }

    /**
     * Get payment status based on rent amounts
     */
    private function getPaymentStatus(): string
    {
        $unpaid = $this->resource['unpaid_renta_raw'] ?? 0;
        $overpaid = $this->resource['overpaid_renta_raw'] ?? 0;
        $totalRent = $this->resource['renta_raw'] ?? 0;

        if ($totalRent == 0) {
            return 'no_rent';
        }

        if ($overpaid > 0) {
            return 'overpaid';
        }

        if ($unpaid > 0) {
            return 'partially_paid';
        }

        return 'fully_paid';
    }

    /**
     * Get additional data to be included with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'version' => '1.0',
                'generated_at' => now()->toISOString(),
            ],
        ];
    }

    /**
     * Customize the response for a request.
     */
    public function withResponse(Request $request, $response): void
    {
        $response->header('X-Resource-Type', 'OwnerPayroll');
        $response->header('X-API-Version', '1.0');
    }
}
