<?php

declare(strict_types=1);

namespace App\Http\Controllers\Payments;

use App\Actions\Payments\GetOwnerPayrollAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Payments\GetOwnerPayrollRequest;
use App\Http\Resources\Payments\OwnerPayrollResource;
use Illuminate\Http\JsonResponse;

/**
 * Payroll Controller
 *
 * Controller for managing owner payroll calculations in the agricultural rent system.
 * This controller handles API endpoints for payroll data retrieval and calculations.
 */
class PayrollController extends Controller
{
    /**
     * Get owner payroll data
     *
     * Returns payroll calculation data for a specific owner or all owners
     * based on the provided criteria. This is the main endpoint for the
     * migrated getOwnerPayroll() functionality.
     *
     * @param GetOwnerPayrollRequest $request
     * @param GetOwnerPayrollAction $action
     * @return JsonResponse
     */
    public function getOwnerPayroll(
        GetOwnerPayrollRequest $request,
        GetOwnerPayrollAction $action
    ): JsonResponse {
        try {
            $result = $action->execute(
                year: $request->validated('year'),
                ownerId: $request->validated('owner_id'),
                path: $request->validated('path'),
                filterParams: $request->validated('filter_params', [])
            );

            return $result;
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve owner payroll data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get owner payroll data (alternative endpoint)
     *
     * Alternative endpoint that returns data using Laravel resources
     * for more structured API responses.
     *
     * @param GetOwnerPayrollRequest $request
     * @param GetOwnerPayrollAction $action
     * @return JsonResponse
     */
    public function index(
        GetOwnerPayrollRequest $request,
        GetOwnerPayrollAction $action
    ): JsonResponse {
        try {
            $data = $action->getData(
                year: $request->validated('year'),
                ownerId: $request->validated('owner_id'),
                path: $request->validated('path'),
                filterParams: $request->validated('filter_params', [])
            );

            return response()->json([
                'success' => true,
                'data' => OwnerPayrollResource::collection($data),
                'meta' => [
                    'total' => count($data),
                    'year' => $request->validated('year'),
                    'owner_id' => $request->validated('owner_id'),
                    'path' => $request->validated('path'),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve owner payroll data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get payroll summary
     *
     * Returns summary statistics for payroll data including totals
     * and aggregated information.
     *
     * @param GetOwnerPayrollRequest $request
     * @param GetOwnerPayrollAction $action
     * @return JsonResponse
     */
    public function summary(
        GetOwnerPayrollRequest $request,
        GetOwnerPayrollAction $action
    ): JsonResponse {
        try {
            $data = $action->getData(
                year: $request->validated('year'),
                ownerId: $request->validated('owner_id'),
                path: $request->validated('path'),
                filterParams: $request->validated('filter_params', [])
            );

            // Calculate summary statistics
            $summary = $this->calculateSummary($data);

            return response()->json([
                'success' => true,
                'data' => $summary,
                'meta' => [
                    'year' => $request->validated('year'),
                    'total_records' => count($data),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payroll summary',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export payroll data
     *
     * Exports payroll data in various formats (CSV, Excel, PDF).
     * This endpoint prepares data for export functionality.
     *
     * @param GetOwnerPayrollRequest $request
     * @param GetOwnerPayrollAction $action
     * @return JsonResponse
     */
    public function export(
        GetOwnerPayrollRequest $request,
        GetOwnerPayrollAction $action
    ): JsonResponse {
        try {
            $data = $action->getData(
                year: $request->validated('year'),
                ownerId: $request->validated('owner_id'),
                path: $request->validated('path'),
                filterParams: array_merge(
                    $request->validated('filter_params', []),
                    ['export_format' => true] // Flag for export formatting
                )
            );

            // Prepare data for export
            $exportData = $this->prepareExportData($data);

            return response()->json([
                'success' => true,
                'data' => $exportData,
                'meta' => [
                    'total_records' => count($exportData),
                    'export_ready' => true,
                    'format' => $request->validated('format', 'json'),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to prepare export data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Calculate summary statistics
     */
    private function calculateSummary(array $data): array
    {
        $summary = [
            'total_owners' => count($data),
            'total_area' => 0.0,
            'total_rent' => 0.0,
            'total_paid' => 0.0,
            'total_unpaid' => 0.0,
            'total_overpaid' => 0.0,
            'payment_rate' => 0.0,
        ];

        foreach ($data as $owner) {
            $summary['total_area'] += $owner['owner_area_raw'] ?? 0;
            $summary['total_rent'] += $owner['renta_raw'] ?? 0;
            $summary['total_paid'] += $owner['paid_renta_raw'] ?? 0;
            $summary['total_unpaid'] += $owner['unpaid_renta_raw'] ?? 0;
            $summary['total_overpaid'] += $owner['overpaid_renta_raw'] ?? 0;
        }

        // Calculate payment rate
        if ($summary['total_rent'] > 0) {
            $summary['payment_rate'] = ($summary['total_paid'] / $summary['total_rent']) * 100;
        }

        // Format for display
        $summary['total_area_formatted'] = number_format($summary['total_area'], 3, ',', ' ') . ' дка';
        $summary['total_rent_formatted'] = number_format($summary['total_rent'], 2, ',', ' ') . ' лв.';
        $summary['total_paid_formatted'] = number_format($summary['total_paid'], 2, ',', ' ') . ' лв.';
        $summary['total_unpaid_formatted'] = number_format($summary['total_unpaid'], 2, ',', ' ') . ' лв.';
        $summary['total_overpaid_formatted'] = number_format($summary['total_overpaid'], 2, ',', ' ') . ' лв.';
        $summary['payment_rate_formatted'] = number_format($summary['payment_rate'], 1) . '%';

        return $summary;
    }

    /**
     * Prepare data for export
     */
    private function prepareExportData(array $data): array
    {
        $exportData = [];

        foreach ($data as $owner) {
            $exportData[] = [
                'owner_id' => $owner['owner_id'],
                'owner_names' => $owner['owner_names'],
                'egn_eik' => $owner['egn_eik'],
                'contract_number' => $owner['c_num'],
                'kad_ident' => $owner['kad_ident'],
                'owner_area' => $owner['owner_area_raw'],
                'rent_amount' => $owner['renta_raw'],
                'paid_amount' => $owner['paid_renta_raw'],
                'unpaid_amount' => $owner['unpaid_renta_raw'],
                'overpaid_amount' => $owner['overpaid_renta_raw'],
                'payment_status' => $this->getPaymentStatus($owner),
                'phone' => $owner['phone'],
                'address' => $owner['address'],
                'iban' => $owner['iban'],
            ];
        }

        return $exportData;
    }

    /**
     * Get payment status for owner
     */
    private function getPaymentStatus(array $owner): string
    {
        $unpaid = $owner['unpaid_renta_raw'] ?? 0;
        $overpaid = $owner['overpaid_renta_raw'] ?? 0;

        if ($overpaid > 0) {
            return 'overpaid';
        } elseif ($unpaid > 0) {
            return 'unpaid';
        } else {
            return 'paid';
        }
    }
}
