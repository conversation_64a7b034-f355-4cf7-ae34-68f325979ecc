<?php

declare(strict_types=1);

use App\Database\Migrations\UserDatabaseMigration;
use Illuminate\Support\Facades\DB;

return new class extends UserDatabaseMigration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('
            CREATE OR REPLACE FUNCTION get_plot_available_areas(
                p_start_date DATE,
                p_due_date DATE
            ) 
            RETURNS TABLE (
                plot_id INTEGER,
                max_overlapping_area NUMERIC
            ) 
            LANGUAGE plpgsql
            AS $$
            BEGIN
                RETURN QUERY
                WITH date_range AS (
                    SELECT generate_series(p_start_date::date, p_due_date::date, interval \'1 year\') AS period_start
                ),
                periods AS (
                    SELECT 
                        period_start,
                        period_start + interval \'1 year - 1 day\' AS period_end
                    FROM date_range
                ),
                contract_details AS (
                    SELECT 
                        c.id as contract_id,
                        c.parent_id,
                        cpr.plot_id as cp_plot_id,
                        ROUND(cpr.contract_area::numeric - 
                            (CASE WHEN MAX(ssc.id) IS NOT NULL 
                             THEN SUM(sscpr.contract_area_for_sale) 
                             ELSE 0 END)::numeric, 3) as cp_contract_area,
                        periods.period_start,
                        periods.period_end
                    FROM periods
                    LEFT JOIN su_contracts c ON c.active = true
                        AND c.start_date <= periods.period_start
                        AND (c.due_date >= periods.period_end OR c.due_date IS NULL)
                    LEFT JOIN su_contracts_plots_rel cpr ON cpr.contract_id = c.id
                        AND cpr.annex_action = \'added\'
                    LEFT JOIN su_contracts an ON an.parent_id = c.id
                        AND an.active = true
                        AND an.start_date <= periods.period_start
                        AND an.due_date >= periods.period_end
                    LEFT JOIN su_sales_contracts_plots_rel sscpr ON sscpr.pc_rel_id = cpr.id
                    LEFT JOIN su_sales_contracts ssc ON ssc.id = sscpr.sales_contract_id
                        AND ssc.start_date <= periods.period_end
                    WHERE c.start_date <= p_due_date
                        AND (c.due_date >= p_start_date OR c.due_date IS NULL)
                        AND an.id IS NULL
                        AND c.active = true
                        AND c.is_sublease = false
                    GROUP BY c.id, c.parent_id, cpr.plot_id, cpr.contract_area, 
                             periods.period_start, periods.period_end
                ),
                overlapping_areas AS (
                    SELECT 
                        cp_plot_id,
                        period_start,
                        period_end,
                        SUM(cp_contract_area) AS overlapping_area
                    FROM contract_details
                    WHERE cp_contract_area > 0
                    GROUP BY cp_plot_id, period_start, period_end
                )
                SELECT 
                    oa.cp_plot_id::INTEGER,
                    MAX(oa.overlapping_area)::NUMERIC
                FROM overlapping_areas oa
                GROUP BY oa.cp_plot_id;
            END;
            $$;
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP FUNCTION IF EXISTS get_plot_available_areas(DATE, DATE);');
    }
};
