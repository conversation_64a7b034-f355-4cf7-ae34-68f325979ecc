<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contracts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->string('c_num');
            $table->unsignedBigInteger('farming_id')->nullable();
            $table->string('farming_name')->nullable();
            $table->string('nm_usage_rights')->nullable();
            $table->string('virtual_contract_type')->nullable();
            $table->date('start_date')->nullable();
            $table->date('due_date')->nullable();
            $table->string('osz_num')->nullable();
            $table->date('osz_date')->nullable();
            $table->string('sv_num')->nullable();
            $table->date('sv_date')->nullable();
            $table->decimal('rent_money_value', 10, 6)->nullable();
            $table->json('rent_nature_json')->nullable();
            $table->unsignedBigInteger('contract_group_id')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['parent_id']);
            $table->index(['farming_id']);
            $table->index(['contract_group_id']);
            $table->index(['c_num']);
            $table->index(['start_date']);
            $table->index(['due_date']);
            $table->index(['virtual_contract_type']);

            // Foreign keys
            $table->foreign('parent_id')->references('id')->on('contracts')->onDelete('cascade');
            $table->foreign('farming_id')->references('id')->on('farmings')->onDelete('set null');
            $table->foreign('contract_group_id')->references('id')->on('contract_groups')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contracts');
    }
};
