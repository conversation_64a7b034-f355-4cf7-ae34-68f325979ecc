<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plots', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('gid')->nullable();
            $table->string('kad_ident')->nullable();
            $table->string('mestnost')->nullable();
            $table->string('category')->nullable();
            $table->string('ekatte', 10)->nullable();
            $table->string('ekatte_name')->nullable();
            $table->string('area_type')->nullable();
            $table->unsignedBigInteger('rent_type_id')->nullable();
            $table->decimal('total_area', 12, 6)->default(0);
            $table->decimal('cultivated_area', 12, 6)->default(0);
            $table->decimal('rent_per_plot_value', 10, 6)->nullable();
            $table->decimal('charged_renta_value', 10, 6)->nullable();
            $table->json('charged_renta_nat_json')->nullable();
            $table->json('renta_nat_json')->nullable();
            $table->decimal('rent_money_value', 10, 6)->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['kad_ident']);
            $table->index(['ekatte']);
            $table->index(['rent_type_id']);
            $table->index(['category']);
            $table->index(['area_type']);

            // Foreign keys
            $table->foreign('rent_type_id')->references('id')->on('rent_types')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plots');
    }
};
