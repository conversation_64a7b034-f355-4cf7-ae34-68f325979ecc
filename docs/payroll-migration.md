# Owner Payroll Migration Documentation

## Overview

This document describes the migration of the `getOwnerPayroll()` functionality from the legacy PHP codebase to Laravel. The migration follows Laravel best practices and maintains the original business logic while modernizing the implementation.

## Architecture

### Original Legacy Structure
- Single monolithic method in `PaymentsController.php`
- Complex nested arrays and manual SQL building
- Mixed business logic and data access
- No separation of concerns

### New Laravel Structure
- **Action Pattern**: Business logic separated into focused Action classes
- **Query Builder**: Modern Laravel database queries replace raw SQL
- **Resource Pattern**: Structured API responses using Laravel Resources
- **Validation**: Dedicated Request classes for input validation
- **Utilities**: Reusable Math and PayrollHelper classes

## Components

### 1. Action Classes (`app/Actions/Payments/`)

#### GetOwnerPayrollAction
- **Purpose**: Main orchestrator for payroll calculations
- **Responsibilities**: Coordinates all sub-actions and returns formatted results
- **Methods**: 
  - `execute()`: Returns JsonResponse for API endpoints
  - `getData()`: Returns raw array data for internal use

#### GetPaymentsPlotsAction
- **Purpose**: Retrieves plot data with rent information
- **Key Features**: 
  - Complex joins using Laravel Query Builder
  - Handles natura rent calculations
  - Supports filtering by owner, contract, and other criteria

#### ProcessOwnerPersonalUseAction
- **Purpose**: Calculates personal use deductions
- **Logic**: Processes personal use amounts and adjusts rent calculations

#### AggregatePaymentPlotsAction
- **Purpose**: Aggregates plot data by different criteria
- **Aggregation Types**: By rent type, by contract, by owner path

#### ContractsPaymentsMappingAction
- **Purpose**: Maps payment data to contract owners
- **Features**: Handles complex owner hierarchies and payment distributions

#### FormattingOwnersDataAction
- **Purpose**: Formats data for display
- **Output**: Structured arrays with both raw and formatted values

### 2. Models (`app/Models/UserDb/`)

#### Enhanced Models
- **Payment**: Enhanced with relationships and scopes
- **Plot**: New model for plot data
- **Transaction**: Model for transaction data
- **NaturaPayment**: Model for natura payment data

### 3. Controller (`app/Http/Controllers/Payments/`)

#### PayrollController
- **Endpoints**:
  - `GET /api/payroll/owner-payroll`: Main payroll data endpoint
  - `GET /api/payroll`: Alternative endpoint with Laravel resources
  - `GET /api/payroll/summary`: Summary statistics
  - `GET /api/payroll/export`: Export-ready data

### 4. Validation (`app/Http/Requests/Payments/`)

#### GetOwnerPayrollRequest
- **Validation Rules**: Comprehensive validation for all parameters
- **Custom Logic**: Owner path validation and filter parameter validation
- **Error Messages**: Localized error messages

### 5. Resources (`app/Http/Resources/Payments/`)

#### OwnerPayrollResource
- **Structure**: Organized data into logical groups (owner, contract, plot, areas, rent)
- **Formatting**: Both raw and formatted values for calculations and display
- **Metadata**: Additional information for frontend processing

### 6. Utilities (`app/Utils/`)

#### Math
- **Purpose**: High-precision mathematical operations using bcmath
- **Methods**: add, sub, mul, div, round, compare, equals, abs, max, min, sum, percentage
- **Features**: Handles null values, string inputs, and precision settings

#### PayrollHelper
- **Purpose**: Agricultural-specific helper functions
- **Key Methods**:
  - `getFarmingYearFromDate()`: Calculates farming year from date
  - `formatCurrency()`, `formatArea()`: Display formatting
  - `bgnToEuro()`: Currency conversion
  - Path manipulation functions

## Configuration

### Payroll Configuration (`config/payroll.php`)
```php
return [
    'precision' => [
        'area' => 3,        // Decimal places for area calculations
        'money' => 2,       // Decimal places for monetary calculations
        'rent_natura' => 3, // Decimal places for natura rent
    ],
    'transaction_types' => [
        'payment' => 1,
    ],
    'farming_year' => [
        'start_month' => 10, // October 1st starts new farming year
        'start_day' => 1,
    ],
    'display' => [
        'date_format' => 'd.m.Y',
        'currency_symbol' => 'лв.',
        'area_unit' => 'дка',
    ],
    'conversion' => [
        'bgn_to_eur_rate' => 1.95583,
    ],
];
```

## API Endpoints

### Main Endpoint
```
GET /api/payroll/owner-payroll
```

**Parameters:**
- `year` (required): Farming year
- `owner_id` (optional): Specific owner ID
- `path` (optional): Owner path for hierarchical data
- `filter_params` (optional): Array of filter parameters
  - `payroll_farming`: Array of farming IDs
  - `owner_names`: Owner name filter
  - `egn`: EGN filter
  - `contract_id`: Contract ID filter
  - `ekatte`: EKATTE code filter
  - `kad_ident`: Cadastral identifier filter
  - `no_contract_payments`: Boolean flag

**Response Structure:**
```json
{
    "success": true,
    "data": [
        {
            "owner": {
                "id": 123,
                "names": "John Doe",
                "egn_eik": "1234567890"
            },
            "areas": {
                "owner_area": {
                    "formatted": "10,500 дка",
                    "raw": 10.5
                }
            },
            "rent": {
                "total_rent": {
                    "formatted": "1 000,00 лв.",
                    "raw": 1000.0,
                    "eur": "511,29 €"
                }
            }
        }
    ],
    "meta": {
        "year": 2024,
        "owner_id": 123,
        "total_records": 1
    }
}
```

## Testing

### Test Coverage
- **Feature Tests**: API endpoint testing (`PayrollControllerTest`)
- **Unit Tests**: 
  - Math utility functions (`MathTest`)
  - PayrollHelper functions (`PayrollHelperTest`)
  - Action class logic (`GetOwnerPayrollActionTest`)

### Running Tests
```bash
# Run all payroll tests
php artisan test --filter=Payroll

# Run specific test classes
php artisan test tests/Feature/Payments/PayrollControllerTest.php
php artisan test tests/Unit/Utils/MathTest.php
```

## Migration Benefits

### Code Quality
- **Separation of Concerns**: Each class has a single responsibility
- **Testability**: All components are unit testable
- **Maintainability**: Clear structure and documentation
- **Type Safety**: Strict typing throughout

### Performance
- **Query Optimization**: Laravel Query Builder with proper indexing
- **Caching**: Built-in Laravel caching capabilities
- **Pagination**: Efficient data loading for large datasets

### Developer Experience
- **IDE Support**: Full autocompletion and type hints
- **Debugging**: Laravel's debugging tools and logging
- **Documentation**: Comprehensive inline documentation

## Usage Examples

### Basic Usage
```php
// Get payroll data for specific year
$action = new GetOwnerPayrollAction();
$result = $action->execute(2024);

// Get data for specific owner
$result = $action->execute(2024, 123);

// Get raw data array
$data = $action->getData(2024, 123);
```

### API Usage
```javascript
// Fetch payroll data
fetch('/api/payroll/owner-payroll?year=2024&owner_id=123')
    .then(response => response.json())
    .then(data => {
        console.log('Payroll data:', data.data);
        console.log('Total records:', data.meta.total_records);
    });
```

## Maintenance

### Adding New Filters
1. Add validation rules to `GetOwnerPayrollRequest`
2. Update `GetPaymentsPlotsAction` to handle new filter
3. Add tests for new functionality

### Modifying Calculations
1. Update relevant Action class
2. Update corresponding tests
3. Verify precision settings in configuration

### Performance Optimization
1. Add database indexes for new query patterns
2. Implement caching for frequently accessed data
3. Consider query optimization for large datasets

## Troubleshooting

### Common Issues
1. **Precision Errors**: Ensure bcmath extension is installed
2. **Memory Issues**: Use pagination for large datasets
3. **Performance**: Check database indexes and query optimization

### Debugging
- Enable Laravel query logging to monitor database queries
- Use Laravel Telescope for request/response debugging
- Check logs in `storage/logs/laravel.log`
